# 移除 version 字段，使用 Compose 规范

services:
  # MongoDB数据库
  mongodb:
    image: mongo:4.4
    container_name: xkd-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_DATABASE: apiServer_xkdgcc
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27018:27017"  # 映射到主机的 27018 端口，避免与本地 MongoDB 冲突
    networks:
      - xkd-network

  # 后台API服务
  api-server:
    build: ./apiServer
    container_name: xkd-api-server
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      MONGODB_URI: mongodb://mongodb:27017/apiServer_xkdgcc
      BASE_URL: http://localhost:3000
    ports:
      - "3000:3000"
    depends_on:
      - mongodb
    networks:
      - xkd-network

  # 前端Web服务
  web-client:
    build:
      context: ./myClient
      dockerfile: Dockerfile.prod
    container_name: xkd-web-client
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - api-server
    networks:
      - xkd-network

volumes:
  mongodb_data:

networks:
  xkd-network:
    driver: bridge