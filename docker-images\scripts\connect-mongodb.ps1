# MongoDB Connection Tool for GCC-XKD
# 用于连接Docker部署的MongoDB数据库

param(
    [string]$Action = "connect",
    [string]$Target = "docker"
)

Write-Host "MongoDB Connection Tool" -ForegroundColor Green
Write-Host "=======================" -ForegroundColor Green

function Show-Usage {
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\connect-mongodb.ps1 [action] [target]" -ForegroundColor White
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  connect  - Connect to MongoDB shell (default)" -ForegroundColor White
    Write-Host "  test     - Test MongoDB connection" -ForegroundColor White
    Write-Host "  status   - Show MongoDB container status" -ForegroundColor White
    Write-Host ""
    Write-Host "Targets:" -ForegroundColor Yellow
    Write-Host "  docker   - Docker MongoDB (port 27018, default)" -ForegroundColor White
    Write-Host "  local    - Local MongoDB (port 27017)" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\connect-mongodb.ps1" -ForegroundColor Cyan
    Write-Host "  .\connect-mongodb.ps1 connect docker" -ForegroundColor Cyan
    Write-Host "  .\connect-mongodb.ps1 test docker" -ForegroundColor Cyan
    Write-Host "  .\connect-mongodb.ps1 status" -ForegroundColor Cyan
}

function Test-MongoConnection {
    param([string]$ConnectionString)
    
    Write-Host ("Testing connection: " + $ConnectionString) -ForegroundColor Yellow
    
    try {
        # Use docker exec to test connection if targeting docker
        if ($Target -eq "docker") {
            $result = & docker exec xkd-mongodb mongo --eval "db.runCommand('ping')" --quiet 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "MongoDB connection successful" -ForegroundColor Green
                return $true
            } else {
                Write-Host "✗ MongoDB connection failed" -ForegroundColor Red
                return $false
            }
        } else {
            # For local MongoDB, try to connect directly
            $result = & mongo $ConnectionString --eval "db.runCommand('ping')" --quiet 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "MongoDB connection successful" -ForegroundColor Green
                return $true
            } else {
                Write-Host "✗ MongoDB connection failed" -ForegroundColor Red
                return $false
            }
        }
    } catch {
        Write-Host ("✗ Connection error: " + $_.Exception.Message) -ForegroundColor Red
        return $false
    }
}

function Connect-MongoDB {
    param([string]$ConnectionString)
    
    Write-Host ("Connecting to MongoDB: " + $ConnectionString) -ForegroundColor Yellow
    Write-Host "Type 'exit' to quit MongoDB shell" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        if ($Target -eq "docker") {
            # Connect via docker exec
            & docker exec -it xkd-mongodb mongo apiServer_xkdgcc
        } else {
            # Connect directly to local MongoDB
            & mongo $ConnectionString
        }
    } catch {
        Write-Host ("Connection error: " + $_.Exception.Message) -ForegroundColor Red
    }
}

function Show-ContainerStatus {
    Write-Host "Docker container status:" -ForegroundColor Yellow
    
    try {
        $containers = & docker ps -a --filter "name=xkd-mongodb" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        if ($containers) {
            Write-Host $containers -ForegroundColor White
        } else {
            Write-Host "No MongoDB containers found" -ForegroundColor Red
        }
    } catch {
        Write-Host ("Error checking container status: " + $_.Exception.Message) -ForegroundColor Red
    }
}

# Determine connection parameters
$connectionString = ""
$port = ""

switch ($Target) {
    "docker" {
        $port = "27018"
        $connectionString = "mongodb://localhost:27018/apiServer_xkdgcc"
        Write-Host ("Target: Docker MongoDB (port " + $port + ")") -ForegroundColor Cyan
    }
    "local" {
        $port = "27017"
        $connectionString = "mongodb://localhost:27017/apiServer_xkdgcc"
        Write-Host ("Target: Local MongoDB (port " + $port + ")") -ForegroundColor Cyan
    }
    default {
        Write-Host ("Unknown target: " + $Target) -ForegroundColor Red
        Show-Usage
        exit 1
    }
}

Write-Host ""

# Execute action
switch ($Action) {
    "connect" {
        if ($Target -eq "docker") {
            # Check if container is running
            $containerStatus = & docker ps --filter "name=xkd-mongodb" --format "{{.Status}}" 2>$null
            if (-not $containerStatus) {
                Write-Host "MongoDB container is not running" -ForegroundColor Red
                Write-Host "Please start the services first: docker-compose up -d" -ForegroundColor Yellow
                exit 1
            }
        }
        
        Connect-MongoDB $connectionString
    }
    "test" {
        Test-MongoConnection $connectionString
    }
    "status" {
        Show-ContainerStatus
    }
    "help" {
        Show-Usage
    }
    default {
        Write-Host ("Unknown action: " + $Action) -ForegroundColor Red
        Show-Usage
        exit 1
    }
}

Write-Host ""
