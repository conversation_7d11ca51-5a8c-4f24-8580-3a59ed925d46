/*
 * @Description: 文件操作类
 * @Author: zpl
 * @Date: 2019-06-20 11:37:44
 * @LastEditors: zpl
 * @LastEditTime: 2019-06-20 13:18:13
 */

const fs = require("fs");

/**
 * 复制文件
 *
 * @param {String} src 原始路径
 * @param {String} dest 目标路径
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 失败回调
 */
const copyFile = (src, dest, onSuccess, onError) => {
  fs.copyFile(src, dest, err => {
    if (err) {
      onError && typeof onError === "function" && onError(err);
    } else {
      onSuccess && typeof onSuccess === "function" && onSuccess();
    }
  });
};

/**
 * 删除文件
 *
 * @param {String} path 路径
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 失败回调
 */
const removeFile = (path, onSuccess, onError) => {
  console.log("removeFile----" + path);
  fs.unlink(path, err => {
    if (err) {
      onError && typeof onError === "function" && onError(err);
    } else {
      onSuccess && typeof onSuccess === "function" && onSuccess();
    }
  });
};

/**
 * 移动文件
 *
 * @param {String} src 原始路径
 * @param {String} dest 目标路径
 * @param {Function} onSuccess 成功回调
 * @param {Function} onError 失败回调
 * @param {Boolean} checkRemove 是否检查删除结果
 */
const moveFile = (src, dest, onSuccess, onError, checkRemove) => {
  copyFile(
    src,
    dest,
    () => {
      removeFile(src, onSuccess, err => {
        if (checkRemove && err) {
          onError && typeof onError === "function" && onError(err);
        } else {
          onSuccess && typeof onSuccess === "function" && onSuccess();
        }
      });
    },
    err => {
      onError && typeof onError === "function" && onError(err);
    }
  );
};

module.exports = {
  copyFile: copyFile,
  removeFile: removeFile,
  moveFile: moveFile,
};
