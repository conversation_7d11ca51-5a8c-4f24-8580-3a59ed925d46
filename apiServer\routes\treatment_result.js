/*
 * @Description: 待遇兑现相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-27 15:49:04
 * @LastEditTime: 2019-05-07 17:54:25
 */

/**
 * Module Dependencies
 */
const api = require('../api/treatment_result');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createTr_r', validator.typeIsJson, api.saveTr_r);
	server.get('/tr_r_list', api.getList);
	server.post('/updateTr_r', validator.typeIsJson, api.updateTr_r);
	server.post('/delTr_r_list', validator.typeIsJson, api.delTr_r_list);
};