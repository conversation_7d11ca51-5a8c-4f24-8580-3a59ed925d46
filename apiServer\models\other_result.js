/**
 * 其他成果表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');
const util = require('../util/util');

const { userUtil } = require('./user');

const OtherResultSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		sbrcch: {// 申报人才称号
			type: String,
			trim: true
		},
		tjrc: {// 推荐人才
			type: String,
			trim: true
		},
		hjqk: {// 获奖
			type: String,
			trim: true
		},
		remark: {// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

OtherResultSchema.plugin(timestamps);

const OtherResult = mongoose.model('other_result', OtherResultSchema, 'other_results');

class OtherResultUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof OtherResultUtil
	 */
	async assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);	
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.sbrcch !== 'undefined' && (target.sbrcch = source.sbrcch);		// 申报人才称号
		typeof source.tjrc !== 'undefined' && (target.tjrc = source.tjrc);		// 推荐人才
		typeof source.hjqk !== 'undefined' && (target.hjqk = source.hjqk);		// 获奖
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		if (typeof source.user_id !== 'undefined') { // 关联查询信息
			if (typeof source.user_id.num != 'undefined') {
				target.user_id = await userUtil.assign(source.user_id);
			} else {
				target.user_id = source.user_id;
			}
		}

		return target;
	}
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source 数据源
	 * @param {Array} years 汇总年份
	 * @param {String} tableDir 时间排列方向，row 横向，col 纵向
	 * @returns
	 * @memberof OtherResultUtil
	 */
	async assign_export(source, years, tableDir) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		util.convertListToYearValue(target, 'sbrcch', source.sbrcch, years, tableDir);		// 申报人才称号
		util.convertListToYearValue(target, 'tjrc', source.tjrc, years, tableDir);		// 推荐人才
		util.convertListToYearValue(target, 'hjqk', source.hjqk, years, tableDir);			// 获奖
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		if (typeof source.user_id !== 'undefined') { // 关联查询信息
			if (typeof source.user_id.num != 'undefined') {
				target.user_id = await userUtil.assign(source.user_id);
			} else {
				target.user_id = source.user_id;
			}
		}

		return target;
	}
}

const otherResultUtil = new OtherResultUtil();

module.exports = { OtherResult, otherResultUtil };    