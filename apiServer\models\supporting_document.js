/**
 * 支撑材料表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');
const util = require('../util/util');

const SupportingDocSchema = new mongoose.Schema(
	{
		title: {// 标题
			"index": true,
			"unique": true,
			"required": true,
			"type": String,
			"trim": true,
			"minlength": 1
		},
		link: {// 链接
			"required": true,
			"type": String,
			"trim": true,
			"minlength": 1
		},
		path: {// 保存路径
			"required": true,
			"type": String,
			"trim": true,
			"minlength": 1
		},
		type: {// 类型
			"type": String,
			"trim": true
		},
		keyword: {// 关键字
			"type": String,
			"trim": true
		},
		uploadUser: {// 上传人
			"type": String,
			"trim": true
		},
		uploadTime: {// 上传时间
			"type": Date,
			"trim": true
		},
		remark: {// 备注
			"type": String,
			"trim": true
		}
	},
	{ autoIndex: false, minimize: false },
);

SupportingDocSchema.plugin(timestamps);

const SupportingDoc = mongoose.model('supporting_document', SupportingDocSchema, 'supporting_documents');

class SupportingDocUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof OtherResultUtil
	 */
	assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}

		source._id && typeof source._id !== 'undefined' && (target._id = source._id);	
		source.title && typeof source.title !== 'undefined' && (target.title = source.title);		// 标题
		source.link && typeof source.link !== 'undefined' && (target.link = source.link);		// 链接
		source.path && typeof source.path !== 'undefined' && (target.path = source.path);		// 保存路径
		source.type && typeof source.type !== 'undefined' && (target.type = source.type);		// 类型
		source.keyword && typeof source.keyword !== 'undefined' && (target.keyword = source.keyword);		// 关键字
		source.uploadUser && typeof source.uploadUser !== 'undefined' && (target.uploadUser = source.uploadUser);		// 上传人
		target.uploadTime = util.getTime(source.uploadTime);	// 上传时间
		source.remark && typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const supportingDocUtil = new SupportingDocUtil();

module.exports = { SupportingDoc, supportingDocUtil };    