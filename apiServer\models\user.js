/**
 * 用户表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');
const util = require('../util/util');
const { userLevel } = require('../dictionary');

const { TreatmentTarget, treatmentTargetUtil } = require('./treatment_target');
const { ThesisTarget, thesisTargetUtil } = require('./thesis_target');
const { ProjectTarget, projectTargetUtil } = require('./project_target');
const { OtherTarget, otherTargetUtil } = require('./other_target');

const UserSchema = new mongoose.Schema(
	{
		num: {	// 人员代码
			"index": true,
			"unique": true,
			"dropDups": true,
			"type": String,
			"required": true,
			"trim": true,
			"minlength": 1
		},
		pwd: {	// 密码
			"type": String,
			"trim": true,
			"minlength": 1,
			"maxlength": 16,
			"default": () => { return 'xkd1234' }
		},
		name: {	// 姓名
			"type": String,
			"required": true,
			"trim": true,
			"minlength": 1
		},
		sex: {	// 性别
			"type": String,
			"required": true,
			"enum": ['男', '女'],
			"trim": true,
			"minlength": 1
		},
		level: {	// 用户级别,0：管理员，1：普通用户，普通用户才有后面的信息
			"type": Number,
			"enum": [userLevel.administrator, userLevel.default],
			"default": 1
		},
		college: {	// 学院，所在单位
			"type": String,
			"trim": true
		},
		political_outlook: {	// 政治面貌
			"type": String,
			"trim": true
		},
		birthday: {	// 出生年月
			"type": Date
		},
		native_place: {	// 籍贯
			"type": String,
			"trim": true
		},
		signing_time: {	// 签约时间
			"type": Date
		},
		enter_date: {	// 入校时间
			"type": Date
		},
		enter_level: {	// 入校层次
			"type": String,
			"trim": true
		},
		title: {	// 职称
			"type": String,
			"trim": true
		},
		review_time: {	// 评审时间
			"type": Date
		},
		Dr_school: {	// 博士毕业学校
			"type": String,
			"trim": true
		},
		Dr_major: {	// 博士专业
			"type": String,
			"trim": true
		},
		graduation_date: {	// 毕业时间
			"type": Date
		},
		Dr_tutor_name: {	// 博导姓名
			"type": String,
			"trim": true
		},
		Dr_tutor_title: {	// 博导称号
			"type": String,
			"trim": true
		},
		overseas: {	// 海外经历
			"type": String,
			"trim": true
		},
		overseas_time: {	// 经历时限
			"type": String,
			"trim": true
		},
		overseas_Dr: {	// 海外博士
			"type": String,
			"trim": true
		},
		after_Dr: {	// 博后经历
			"type": String,
			"trim": true
		},
		status: { // 人员状态
			"type": String,
			"trim": true
		},
		mobile: { // 联系方式
			"type": String,
			"trim": true
		}
	},
	{ autoIndex: false, minimize: false },
);

UserSchema.plugin(timestamps);

const User = mongoose.model('user', UserSchema, 'users');

class UserUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @param {Boolean} getPwd 是否获取密码
	 * @param {Boolean} getTreatment_t 是否获取待遇协议
	 * @param {Boolean} getThesis_t 是否获取论文目标
	 * @param {Boolean} getProject_t 是否获取项目目标
	 * @param {Boolean} getOther_t 是否获取其他目标
	 * @returns
	 * @memberof UserUtil
	 */
	async assign(source, getPwd, getTreatment_t, getThesis_t, getProject_t, getOther_t) {
		let target = {};

		// 人员代码
		if (!source || !source.num || typeof source.num === 'undefined' || source.num === '') {
			return target;
		}

		target.num = source.num;
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);
		source.num === 'admin' && (target.disabled = true);	// 配合前端table，如果是admin则禁止操作
		getPwd && (typeof source.pwd !== 'undefined' && source.pwd !== '') && (target.pwd = source.pwd);		// 密码
		typeof source.name !== 'undefined' && (target.name = source.name);	// 姓名
		typeof source.sex !== 'undefined' && (target.sex = source.sex);		// 性别
		typeof source.level !== 'undefined' && (target.level = source.level);	// 用户级别
		typeof source.college !== 'undefined' && (target.college = source.college);	// 所在单位
		typeof source.political_outlook !== 'undefined' && (target.political_outlook = source.political_outlook);	// 政治面貌
		target.birthday = util.getTime(source.birthday);	// 出生年月
		typeof source.native_place !== 'undefined' && (target.native_place = source.native_place);	// 籍贯
		target.signing_time = util.getTime(source.signing_time);	// 签约时间
		target.enter_date = util.getTime(source.enter_date);	// 入校时间
		typeof source.enter_level !== 'undefined' && (target.enter_level = source.enter_level);	// 入校层次
		typeof source.title !== 'undefined' && (target.title = source.title);	// 职称
		target.review_time = util.getTime(source.review_time);	// 评审时间
		typeof source.Dr_school !== 'undefined' && (target.Dr_school = source.Dr_school);	// 博士毕业学校
		typeof source.Dr_major !== 'undefined' && (target.Dr_major = source.Dr_major);	// 博士专业
		target.graduation_date = util.getTime(source.graduation_date);	// 毕业时间
		typeof source.Dr_tutor_name !== 'undefined' && (target.Dr_tutor_name = source.Dr_tutor_name);	// 博导姓名
		typeof source.Dr_tutor_title !== 'undefined' && (target.Dr_tutor_title = source.Dr_tutor_title);	// 博导称号
		typeof source.overseas !== 'undefined' && (target.overseas = source.overseas);	// 海外经历
		typeof source.overseas_time !== 'undefined' && (target.overseas_time = source.overseas_time);	// 经历时限
		typeof source.overseas_Dr !== 'undefined' && (target.overseas_Dr = source.overseas_Dr);	// 海外博士
		typeof source.after_Dr !== 'undefined' && (target.after_Dr = source.after_Dr);	// 博后经历
		typeof source.status !== 'undefined' && (target.status = source.status);	// 人员状态
		typeof source.mobile !== 'undefined' && (target.mobile = source.mobile);	// 联系方式

		if (getTreatment_t) {
			const treatment_t = await TreatmentTarget.findOne({ num: target.num });
			target.treatment_t = treatmentTargetUtil.assign(treatment_t);
		}

		if (getThesis_t) {
			const thesis_t = await ThesisTarget.findOne({ num: target.num });
			target.thesis_t = thesisTargetUtil.assign(thesis_t);
		}

		if (getProject_t) {
			const project_t = await ProjectTarget.findOne({ num: target.num });
			target.project_t = projectTargetUtil.assign(project_t);
		}

		if (getOther_t) {
			const other_t = await OtherTarget.findOne({ num: target.num });
			target.other_t = otherTargetUtil.assign(other_t);
		}

		return target;
	}

	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof UserUtil
	 */
	assign_export(source) {
		let target = {};

		// 人员代码
		if (!source || !source.num || typeof source.num === 'undefined' || source.num === '') {
			return target;
		}

		target.num = source.num;
		typeof source.name !== 'undefined' && (target.name = source.name);	// 姓名
		typeof source.sex !== 'undefined' && (target.sex = source.sex);		// 性别
		typeof source.level !== 'undefined' && (target.level = source.level);	// 用户级别
		typeof source.college !== 'undefined' && (target.college = source.college);	// 所在单位
		typeof source.political_outlook !== 'undefined' && (target.political_outlook = source.political_outlook);	// 政治面貌
		const birthday = util.getTime(source.birthday);
		target.birthday = birthday ? `${new Date(birthday).getFullYear()}年${new Date(birthday).getMonth() + 1}月` : ``;	// 出生年月
		typeof source.native_place !== 'undefined' && (target.native_place = source.native_place);	// 籍贯
		const signing_time = util.getTime(source.signing_time);
		target.signing_time = signing_time ? `${new Date(signing_time).getFullYear()}年${new Date(signing_time).getMonth() + 1}月${new Date(signing_time).getDate()}日` : ``;	// 签约时间
		const enter_date = util.getTime(source.enter_date);
		target.enter_date = enter_date ? `${new Date(enter_date).getFullYear()}年${new Date(enter_date).getMonth() + 1}月` : ``;	// 入校/选/站时间
		typeof source.enter_level !== 'undefined' && (target.enter_level = source.enter_level);	// 入校层次
		typeof source.title !== 'undefined' && (target.title = source.title);	// 职称
		const review_time = util.getTime(source.review_time);
		target.review_time = review_time ? `${new Date(review_time).getFullYear()}年${new Date(review_time).getMonth() + 1}月` : ``;	// 评审时间
		typeof source.Dr_school !== 'undefined' && (target.Dr_school = source.Dr_school);	// 博士毕业学校
		typeof source.Dr_major !== 'undefined' && (target.Dr_major = source.Dr_major);	// 博士专业
		const graduation_date = util.getTime(source.graduation_date);
		target.graduation_date = graduation_date ? `${new Date(graduation_date).getFullYear()}年${new Date(graduation_date).getMonth() + 1}月` : ``;	// 毕业时间
		typeof source.Dr_tutor_name !== 'undefined' && (target.Dr_tutor_name = source.Dr_tutor_name);	// 博导姓名
		typeof source.Dr_tutor_title !== 'undefined' && (target.Dr_tutor_title = source.Dr_tutor_title);	// 博导称号
		typeof source.overseas !== 'undefined' && (target.overseas = source.overseas);	// 海外经历
		typeof source.overseas_time !== 'undefined' && (target.overseas_time = source.overseas_time);	// 经历时限
		typeof source.overseas_Dr !== 'undefined' && (target.overseas_Dr = source.overseas_Dr);	// 海外博士
		typeof source.after_Dr !== 'undefined' && (target.after_Dr = source.after_Dr);	// 博后经历
		typeof source.status !== 'undefined' && (target.status = source.status);	// 人员状态
		typeof source.mobile !== 'undefined' && (target.mobile = source.mobile);	// 联系方式

		return target;
	}
}
const userUtil = new UserUtil();

module.exports = { User, userUtil };    