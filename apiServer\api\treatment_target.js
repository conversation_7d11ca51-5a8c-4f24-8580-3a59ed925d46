/*
 * @Description: 待遇协议相关接口
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-05-05 17:52:32
 * @LastEditTime: 2019-05-21 22:52:06
 */
/**
 * Model Schema
 */
const { User } = require('../models/user');
const { TreatmentTarget, treatmentTargetUtil } = require('../models/treatment_target');

const { userLevel } = require('../dictionary');

/**
   * 创建记录
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
const saveTr_t = (req, res, next) => {
  console.log('treatment_target saveTr_t');
  let data = req.body || {};

  const num = data.num;
  User.findOne({ num: num }).then(result => {
    if (result) {
      if (result.level === userLevel.administrator) {
        return Promise.reject({ "status": "error", "message": "管理员账号不能创建业务信息。" });
      }
      let treatmentTData = treatmentTargetUtil.assign(data);
      treatmentTData.user_id = result._id;
      let treatmentT = new TreatmentTarget(treatmentTData);
      return treatmentT.save();
    } else {
      return Promise.reject({ "status": "error", "message": "用户不存在，请先创建基础信息。" });
    }
  }).then(result => {
    res.send({ "result": result, "status": "ok" });
  }).catch(err => {
    console.error(err);
    if (err.status === "error") {
      res.send(err);
    } else {
      next(err);
    }
  });
}

/**
 * 查询列表
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const getList = (req, res, next) => {
  console.log('treatment_target getList');
  let {
    num,
    name,
    sorter,
    pageSize = 1000,
    currentPage = 1
  } = req.params;

  // 筛选条件
  let queryJson = {};
  if (num) { // 精确查询
    queryJson = { num: num };
    currentPage = 1;
  } else if (name) { // 模糊查询
    queryJson = { "$or": [{ num: name }, { name: new RegExp(name, 'i') }] };
    currentPage = 1;
  }

  // 排序条件
  let sortJson = {};
  if (sorter) {
    const s = sorter.split('_');
    const sortName = s[0];
    const dir = s[1] === 'descend' ? -1 : 1;
    sortJson = {};
    sortJson[sortName] = dir;
  }
  TreatmentTarget.find(queryJson, null, {
    sort: sortJson,
    limit: parseInt(pageSize),
    skip: (currentPage - 1) * pageSize
  }).populate('user_id', 'name').then(list => {
    res.send({
      "status": "ok",
      "list": list
    });
  }).catch(err => {
    console.error(err);
    next(err);
  });
}

/**
 * 更新
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const updateTr_t = (req, res, next) => {
  console.log('treatment_target updateTr_t');
  let data = req.body || {};

  User.findOne({ num: data.num }).then(doc => {
    if (!doc) {
      return Promise.reject({ "status": "error", "message": "人员代码无效。" });
    }
    if (doc.level === userLevel.administrator) {
      return Promise.reject({ "status": "error", "message": "管理员账号不能创建业务信息。" });
    }
  }).then(() => {
    return TreatmentTarget.findOne({ num: data.num });
  }).then(doc => {
    if (!doc) {
      return Promise.reject({ "status": "create" });
    }
    return doc.updateOne(treatmentTargetUtil.assign(data));
  }).then(() => {
    res.send({ "status": "ok" });
  }).catch(err => {
    console.error(err);
    if (err.status === "create") {
      saveTr_t(req, res, next);
    } else if (err.status === "error") {
      res.send(err);
    } else {
      next(err);
    }
  });
}

/**
 * 批量删除
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const delTr_t_list = (req, res, next) => {
  console.log('treatment_target delTr_t_list');
  let data = req.body || {};
  let { numList } = data;

  TreatmentTarget.deleteMany({ num: { $in: numList } }).then(() => {
    res.send({ "status": "ok" });
  }).catch(err => {
    console.error(err);
    next(err);
  });
}

module.exports = {
  saveTr_t: saveTr_t,
  getList: getList,
  updateTr_t: updateTr_t,
  delTr_t_list: delTr_t_list
}