/*
 * @Description: 支撑材料相关接口
 * @Author: zpl
 * @Created Date: 2019-06-14 09:32:29
 */

/**
 * Module Dependencies
 */
const mongoose = require("mongoose");
const fileManager = require("../util/file_manager");

/**
 * Model Schema
 */
const {
  SupportingDoc,
  supportingDocUtil,
} = require("../models/supporting_document");

/**
 * 创建记录
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const saveSupportingDoc = (req, res, next) => {
  console.log("supportingDoc saveSupportingDoc");
  let data = req.body || {};

  SupportingDoc.findOne({ title: data.title })
    .then(doc => {
      if (doc) {
        return Promise.reject({
          status: "error",
          message: "名称已存在，请重命名后再试。",
        });
      } else {
        let supportingDoc = new SupportingDoc(supportingDocUtil.assign(data));
        return supportingDoc.save();
      }
    })
    .then(result => {
      res.send({ result: result, status: "ok" });
    })
    .catch(err => {
      console.error(err);
      if (err.status === "error") {
        res.send(err);
      } else {
        res.send({
          status: "error",
          message: err.message,
        });
      }
    });
};

/**
 * 查询列表
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const getList = (req, res, next) => {
  console.log("supportingDoc getList");
  let { title, sorter, pageSize = 1000, currentPage = 1 } = req.params;

  // 筛选条件
  let queryJson = {};
  if (title) {
    queryJson = { title: new RegExp(title, "i") };
    currentPage = 1;
  }

  // 排序条件
  let sortJson = { updateAt: 1 };
  if (sorter) {
    const s = sorter.split("_");
    const sortName = s[0];
    const dir = s[1] === "descend" ? -1 : 1;
    sortJson = {};
    sortJson[sortName] = dir;
  }

  let total = 0;
  Promise.all([
    SupportingDoc.find(queryJson).countDocuments(),
    SupportingDoc.find(queryJson, null, {
      sort: sortJson,
      limit: parseInt(pageSize),
      skip: (currentPage - 1) * pageSize,
    }).populate("user_id"),
  ])
    .then(list => {
      total = list[0];
      let results = list[1];

      let returnList = [];
      if (results && results.length) {
        returnList = results.map(result => {
          return supportingDocUtil.assign(result);
        });
      }
      res.send({
        status: "ok",
        list: returnList,
        pagination: {
          currentPage: parseInt(currentPage),
          pageSize: parseInt(pageSize),
          total: total,
        },
      });
    })
    .catch(err => {
      console.error(err);
      next(err);
    });
};

/**
 * 更新
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const updateSupportingDoc = (req, res, next) => {
  console.log("supportingDoc updateSupportingDoc");
  let data = req.body || {};
  if (typeof data._id == "string") {
    data._id = mongoose.Types.ObjectId(data._id);
  }

  Promise.all([
    SupportingDoc.findOne({ _id: data._id }),
    SupportingDoc.findOne({ title: data.title }),
  ])
    .then(list => {
      const doc = list[0];
      if (!doc) {
        return Promise.reject({ status: "error", message: "无效ID。" });
      }
      if (list[1] && data.title !== doc.title) {
        return Promise.reject({
          status: "error",
          message: "名称已存在，请重命名后再试。",
        });
      }
      // 为防止文件保存混乱，不允许修改链接和保存路径
      data.link = null;
      data.path = null;
      return doc.updateOne(supportingDocUtil.assign(data));
    })
    .then(() => {
      res.send({ status: "ok" });
    })
    .catch(err => {
      console.error(err);
      if (err.status === "error") {
        res.send(err);
      } else {
        next(err);
      }
    });
};

/**
 * 批量删除
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const delSupportingDoc_list = (req, res, next) => {
  console.log("supportingDoc delSupportingDoc_list");
  let data = req.body || {};
  let { _idList } = data;

  if (_idList.length) {
    if (typeof _idList[0] == "string") {
      _idList = _idList.map(id => {
        return mongoose.Types.ObjectId(id);
      });
    }
    fileManager.removeFile("", () => {}, err => {});
    SupportingDoc.find({ _id: { $in: _idList } })
      .then(list => {
        if (list && list.length) {
          list.map(doc => {
            fileManager.removeFile(doc.path);
          });
          return SupportingDoc.deleteMany({ _id: { $in: _idList } });
        } else {
          return Promise.reject({
            status: error,
            message: "删除0条记录",
          });
        }
      })
      .then(() => {
        res.send({ status: "ok" });
      })
      .catch(err => {
        console.error(err);
        if (err.status === "error") {
          res.send(err);
        } else {
          next(err);
        }
      });
  } else {
    res.send({ status: "error", message: "无效的id。" });
  }
};

module.exports = {
  saveSupportingDoc: saveSupportingDoc,
  getList: getList,
  updateSupportingDoc: updateSupportingDoc,
  delSupportingDoc_list: delSupportingDoc_list,
};
