# GCC-XKD Stop Script (Windows)

Write-Host "Stopping GCC-XKD services..." -ForegroundColor Green

if (Test-Path "docker-compose.yml") {
    & docker-compose down
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services stopped" -ForegroundColor Green
    } else {
        Write-Host "Error occurred while stopping services" -ForegroundColor Red
    }
} else {
    Write-Host "docker-compose.yml configuration file not found" -ForegroundColor Red
    Write-Host "Please ensure you run this script in the correct directory" -ForegroundColor Yellow
}

Read-Host "Press any key to exit"
