/**
 * 论文目标表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');

const ThesisTargetSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {	// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		sum: {	// 总数
			type: String,
			trim: true
		},
		sci: {	// SCI/C刊
			type: String,
			trim: true
		},
		sci1: {	// SCI1
			type: String,
			trim: true
		},
		sci2: {	// SCI2
			type: String,
			trim: true
		},
		remark: {	// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

ThesisTargetSchema.plugin(timestamps);

const ThesisTarget = mongoose.model('thesis_target', ThesisTargetSchema, 'thesis_targets');

class ThesisTargetUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ThesisTargetUtil
	 */
	assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.sum !== 'undefined' && (target.sum = source.sum);		// 总数
		typeof source.sci !== 'undefined' && (target.sci = source.sci);		// SCI/C刊
		typeof source.sci1 !== 'undefined' && (target.sci1 = source.sci1);		// SCI1
		typeof source.sci2 !== 'undefined' && (target.sci2 = source.sci2);		// SCI2
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const thesisTargetUtil = new ThesisTargetUtil();

module.exports = { ThesisTarget, thesisTargetUtil };    