/*
 * @Author: zpl
 * @LastEditors: zpl
 * @Description: 初始化文件夹
 * @Date: 2019-04-30 10:34:56
 * @LastEditTime: 2019-06-19 18:40:39
 */

/**
 * Module Dependencies
 */
const fs = require('fs');

const dictionary = require('../dictionary');
const uploadPath = dictionary.upload.savePath;
const exportPath = dictionary.export.savePath;

module.exports = async function () {
  fs.access(uploadPath, fs.constants.F_OK, err => {
    if (err) {
      console.log('创建上传文件夹...');
      fs.mkdir(uploadPath, { recursive: true }, (err) => {
        if (err) {
          console.log('上传文件夹创建失败！');
          console.dir(err);
        } else {
          console.log('上传文件夹创建成功！');
        }
      });
    }
  });
  fs.access(exportPath, fs.constants.F_OK, err => {
    if (err) {
      console.log('创建导出文件夹...');
      fs.mkdir(exportPath, { recursive: true }, (err) => {
        if (err) {
          console.log('导出文件夹创建失败！');
          console.dir(err);
        } else {
          console.log('导出文件夹创建成功！');
        }
      });
    }
  });
};