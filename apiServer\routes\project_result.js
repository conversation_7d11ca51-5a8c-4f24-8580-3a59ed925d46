/*
 * @Description: 项目成果相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-27 17:26:10
 * @LastEditTime: 2019-05-08 12:52:06
 */

/**
* Module Dependencies
*/
const api = require('../api/project_result');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createPr_r', validator.typeIsJson, api.savePr_r);
	server.get('/pr_r_list', api.getList);
	server.post('/updatePr_r', validator.typeIsJson, api.updatePr_r);
	server.post('/delPr_r_list', validator.typeIsJson, api.delPr_r_list);
};