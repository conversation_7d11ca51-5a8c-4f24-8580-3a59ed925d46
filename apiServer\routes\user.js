/*
 * @Author: zpl
 * @LastEditors: zpl
 * @Description: 用户相关路由
 * @Date: 2019-04-22 21:48:52
 * @LastEditTime: 2019-05-05 18:25:41
 */

/**
* Module Dependencies
*/
const userApi = require('../api/user');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createUser', validator.typeIsJson, userApi.saveUser);
	server.get('/users', userApi.getList);
	server.get('/user/:user_num', userApi.getUserByNum);
	server.post('/updateUser', validator.typeIsJson, userApi.updateUserInfo);
	server.post('/delUsers', validator.typeIsJson, userApi.deleteUsers);
};