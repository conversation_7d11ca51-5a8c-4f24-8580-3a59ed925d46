/*
 * @Description: 其他成果相关接口
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-05-01 12:51:06
 * @LastEditTime: 2019-05-21 23:00:29
 */

/**
* Module Dependencies
*/
const mongoose = require('mongoose');

/**
 * Model Schema
 */
const { User } = require("../models/user");
const { OtherResult, otherResultUtil } = require('../models/other_result');

const { userLevel } = require('../dictionary');

/**
   * 创建记录
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
const saveOt_r = (req, res, next) => {
  console.log('other_result saveOt_r');
  let data = req.body || {};

  const num = data.num;
  let user = {};
  User.findOne({ num: num }).then(result => {
    if (result) {
      if (result.level === userLevel.administrator) {
        return Promise.reject({ "status": "error", "message": "管理员账号不能创建业务信息。" });
      }
      user = result;
      data.user_id = result._id;
      return otherResultUtil.assign(data);;
    } else {
      return Promise.reject({ "status": "error", "message": "用户不存在，请先创建基础信息。" });
    }
  }).then(otherRData => {
    let otherR = new OtherResult(otherRData);
    return otherR.save();
  }).then(result => {
    result.user_id = user;
    res.send({ "result": result, "status": "ok" });
  }).catch(err => {
    console.error(err);
    if (err.status === "error") {
      res.send(err);
    } else {
      next(err);
    }
  });
}

/**
 * 查询列表
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const getList = (req, res, next) => {
  console.log('other_result getList');
  let {
    num,
    name,
    sorter,
    pageSize = 1000,
    currentPage = 1
  } = req.params;

  // 筛选条件
  let queryJson = {};
  if (num) {
    queryJson = { num: num };
    currentPage = 1;
  } else if (name) {
    queryJson = { "$or": [{ num: name }, { name: new RegExp(name, 'i') }] };
    currentPage = 1;
  }

  // 排序条件
  let sortJson = { num: 1 };
  if (sorter) {
    const s = sorter.split('_');
    const sortName = s[0];
    const dir = s[1] === 'descend' ? -1 : 1;
    sortJson = {};
    sortJson[sortName] = dir;
  }

  let total = 0;
  Promise.all([
    OtherResult.find(queryJson).countDocuments(),
    OtherResult.find(queryJson, null, {
      sort: sortJson,
      limit: parseInt(pageSize),
      skip: (currentPage - 1) * pageSize
    }).populate('user_id')
  ]).then(list => {
    total = list[0];
    let results = list[1];

    let returnList = [];
    if (results && results.length) {
      returnList = results.map(result => {
        return otherResultUtil.assign(result);
      });
    }
    return Promise.all(returnList);
  }).then(list => {
    res.send({
      "status": "ok",
      "list": list,
      "pagination": {
        "currentPage": parseInt(currentPage),
        "pageSize": parseInt(pageSize),
        "total": total,
      },
    });
  }).catch(err => {
    console.error(err);
    next(err);
  });
}

/**
 * 更新
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const updateOt_r = (req, res, next) => {
  console.log('other_result updateOt_r');
  let data = req.body || {};
  if (typeof (data._id) == 'string') {
    data._id = mongoose.Types.ObjectId(data._id);
  }

  let ot_r;
  OtherResult.findOne({ _id: data._id }).then(doc => {
    if (!doc) {
      return Promise.reject({ "status": "error", "message": "无效ID。" });
    }
    ot_r = doc;
    // TODO: 禁止修改关联num
    return otherResultUtil.assign(data);
  }).then(info => {
    return ot_r.updateOne(info);
  }).then(() => {
    res.send({ "status": "ok" });
  }).catch(err => {
    console.error(err);
    if (err.status === "error") {
      res.send(err);
    } else {
      next(err);
    }
  });
}

/**
 * 批量删除
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const delOt_r_list = (req, res, next) => {
  console.log('other_result delOt_r_list');
  let data = req.body || {};
  let { num, _idList } = data;

  let queryJson;
  if (num) {
    queryJson = { num: num };
  } else if (_idList.length) {
    if (typeof (_idList[0]) == 'string') {
      _idList = _idList.map(id => {
        return mongoose.Types.ObjectId(id);
      });
    }
    queryJson = { _id: { $in: _idList } }
  }

  if (queryJson) {
    OtherResult.deleteMany(queryJson).then(() => {
      res.send({ "status": "ok" });
    }).catch(err => {
      console.error(err);
      next(err);
    });
  } else {
    res.send({ 'status': 'error', 'message': '无效的id或人员代码。' });
  }
}

module.exports = {
  saveOt_r: saveOt_r,
  getList: getList,
  updateOt_r: updateOt_r,
  delOt_r_list: delOt_r_list
}