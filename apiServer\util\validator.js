/*
 * @Description: 验证类
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-05-01 13:09:03
 * @LastEditTime: 2019-05-05 18:25:16
 */

const errors = require('restify-errors');

module.exports = {
  typeIsJson: (req, res, next) => {
    if (!req.is('application/json')) {
      // TODO: 返回什么格式的内容前台才能正常提示
      res.send({ "status": "error", "message": "必须以application/json格式发送请求。" });
    } else {
      next();
    }
  }
}