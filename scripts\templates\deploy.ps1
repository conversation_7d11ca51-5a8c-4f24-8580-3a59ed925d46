# GCC-XKD One-Click Deployment Script (Windows)

param([switch]$Force)

Write-Host "GCC-XKD One-Click Deployment Tool" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Check Docker environment
function Test-DockerEnvironment {
    Write-Host "Checking Docker environment..." -ForegroundColor Yellow

    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker service not started" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker installed and running" -ForegroundColor Green
    } catch {
        Write-Host "Docker not installed" -ForegroundColor Red
        return $false
    }

    try {
        & docker-compose version 2>$null | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker Compose not installed" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker Compose available" -ForegroundColor Green
    } catch {
        Write-Host "Docker Compose not installed" -ForegroundColor Red
        return $false
    }

    return $true
}

# Check deployment package integrity
function Test-DeploymentPackage {
    Write-Host "Checking deployment package integrity..." -ForegroundColor Yellow

    $requiredDirs = @("images", "config", "scripts")
    foreach ($dir in $requiredDirs) {
        if (-not (Test-Path $dir)) {
            Write-Host ("Missing directory: " + $dir) -ForegroundColor Red
            return $false
        }
        Write-Host ("Found directory: " + $dir) -ForegroundColor Green
    }

    if (-not (Test-Path "config/docker-compose.yml")) {
        Write-Host "Missing configuration file: docker-compose.yml" -ForegroundColor Red
        return $false
    }
    Write-Host "Docker Compose configuration file exists" -ForegroundColor Green

    $tarFiles = Get-ChildItem "images" -Filter "*.tar"
    if ($tarFiles.Count -eq 0) {
        Write-Host "No image files found" -ForegroundColor Red
        return $false
    }
    Write-Host ("Found " + $tarFiles.Count + " image files") -ForegroundColor Green

    return $true
}

# Import images
function Import-DockerImages {
    Write-Host "Importing Docker images..." -ForegroundColor Yellow

    if (-not (Test-Path "images")) {
        Write-Host "Images directory not found" -ForegroundColor Red
        return $false
    }

    $tarFiles = Get-ChildItem "images" -Filter "*.tar"

    if ($tarFiles.Count -eq 0) {
        Write-Host "No image files found" -ForegroundColor Red
        return $false
    }

    foreach ($tarFile in $tarFiles) {
        Write-Host ("Importing: " + $tarFile.Name) -ForegroundColor Cyan
        & docker load -i $tarFile.FullName
        if ($LASTEXITCODE -eq 0) {
            Write-Host ("Import completed: " + $tarFile.Name) -ForegroundColor Green
        } else {
            Write-Host ("Import failed: " + $tarFile.Name) -ForegroundColor Red
            return $false
        }
    }

    Write-Host "Image import completed" -ForegroundColor Green
    return $true
}

# Start services
function Start-Services {
    Write-Host "Starting services..." -ForegroundColor Yellow

    # Copy configuration file to current directory
    if (Test-Path "config/docker-compose.yml") {
        Copy-Item "config/docker-compose.yml" . -Force
        Write-Host "Configuration file copied to current directory" -ForegroundColor Green
    } else {
        Write-Host "docker-compose.yml file not found" -ForegroundColor Red
        return $false
    }

    # Start services
    & docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services started successfully" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Failed to start services" -ForegroundColor Red
        return $false
    }
}

# Show access information
function Show-AccessInfo {
    Write-Host ""
    Write-Host "Deployment completed!" -ForegroundColor Green
    Write-Host "====================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access URLs:" -ForegroundColor Cyan
    Write-Host "- Frontend: http://localhost" -ForegroundColor White
    Write-Host "- Backend API: http://localhost:3000" -ForegroundColor White
    Write-Host "- MongoDB: localhost:27018" -ForegroundColor White
    Write-Host ""
    Write-Host "Common commands:" -ForegroundColor Cyan
    Write-Host "- Check status: docker-compose ps" -ForegroundColor White
    Write-Host "- View logs: docker-compose logs -f" -ForegroundColor White
    Write-Host "- Stop services: docker-compose down" -ForegroundColor White
    Write-Host "- Restart services: docker-compose restart" -ForegroundColor White
    Write-Host ""
    Write-Host "For help, please check README.md documentation" -ForegroundColor Yellow
}

# Main process
Write-Host ""

# 1. Check environment
if (-not (Test-DockerEnvironment)) {
    Write-Host ""
    Write-Host "Please install and start Docker environment first" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 2. Check deployment package integrity
if (-not (Test-DeploymentPackage)) {
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 3. Import images
if (-not (Import-DockerImages)) {
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 4. Start services
if (-not (Start-Services)) {
    Read-Host "Press any key to exit"
    exit 1
}

# 5. Show access information
Show-AccessInfo

Write-Host ""
Read-Host "Press any key to exit"
