/*
 * @Description: 项目目标相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-27 15:27:18
 * @LastEditTime: 2019-05-06 11:43:16
 */

const userApi = require('../api/project_target');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createPr_t', validator.typeIsJson, userApi.savePr_t);
	server.get('/pr_t_list', userApi.getList);
	server.post('/updatePr_t', validator.typeIsJson, userApi.updatePr_t);
	server.post('/delPr_t_list', validator.typeIsJson, userApi.delPr_t_list);
};