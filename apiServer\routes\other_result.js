/*
 * @Description: 其他成果相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-27 17:26:13
 * @LastEditTime: 2019-05-08 12:52:23
 */

/**
 * Module Dependencies
 */
const api = require('../api/other_result');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createOt_r', validator.typeIsJson, api.saveOt_r);
	server.get('/ot_r_list', api.getList);
	server.post('/updateOt_r', validator.typeIsJson, api.updateOt_r);
	server.post('/delOt_r_list', validator.typeIsJson, api.delOt_r_list);
};