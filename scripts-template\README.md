# GCC-XKD 离线部署包

## 部署包说明

这是一个完整的离线部署包，包含了运行 GCC-XKD 高层次人才服务管理平台所需的所有 Docker 镜像和配置文件。

## 部署包结构

```
docker-images/
├── images/                    # Docker 镜像文件
│   ├── gcc-xkd-api-server_latest.tar
│   ├── gcc-xkd-web-client_latest.tar
│   ├── mongo_4.4.tar         # (如果包含基础镜像)
│   ├── node_16-alpine.tar    # (如果包含基础镜像)
│   └── nginx_alpine.tar      # (如果包含基础镜像)
├── config/                    # 配置文件
│   └── docker-compose.yml    # 部署配置文件
├── scripts/                   # 部署和管理脚本
│   ├── deploy-offline.ps1    # 离线部署脚本(Windows)
│   ├── deploy-offline.sh     # 离线部署脚本(Linux/macOS)
│   ├── connect-mongodb.ps1   # MongoDB连接工具(Windows)
│   └── connect-mongodb.sh    # MongoDB连接工具(Linux/macOS)
├── README.md                  # 本文件
└── IMAGES.md                  # 镜像清单
```

## 系统要求

### 必需软件
- **Docker**: >= 20.x
- **Docker Compose**: >= 1.29.x

### 端口要求
确保以下端口未被占用：
- **80**: 前端Web服务
- **3000**: 后台API服务
- **27018**: MongoDB数据库

### 操作系统支持
- Windows 10/11
- Linux (Ubuntu, CentOS, RHEL等)
- macOS

## 快速部署

### 方法1：一键部署（推荐）

**Windows PowerShell:**
```powershell
# 进入部署包目录
cd docker-images

# 运行一键部署脚本
.\scripts\deploy-offline.ps1
```

**Linux/macOS:**
```bash
# 进入部署包目录
cd docker-images

# 添加执行权限
chmod +x scripts/*.sh

# 运行一键部署脚本
./scripts/deploy-offline.sh
```

### 方法2：手动部署

**1. 导入镜像**
```bash
# 导入所有镜像文件
for file in images/*.tar; do docker load -i "$file"; done
```

**2. 复制配置文件**
```bash
cp config/docker-compose.yml .
```

**3. 启动服务**
```bash
docker-compose up -d --no-build
```

## 访问应用

部署成功后，可以通过以下地址访问：

- **前端应用**: http://localhost
- **后台API**: http://localhost:3000
- **MongoDB**: localhost:27018

## 管理命令

### 服务管理
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重启单个服务
docker-compose restart api-server
docker-compose restart web-client
docker-compose restart mongodb
```

### 数据库管理

**Windows:**
```powershell
# 连接MongoDB
.\scripts\connect-mongodb.ps1

# 测试连接
.\scripts\connect-mongodb.ps1 test

# 查看容器状态
.\scripts\connect-mongodb.ps1 status
```

**Linux/macOS:**
```bash
# 连接MongoDB
./scripts/connect-mongodb.sh

# 测试连接
./scripts/connect-mongodb.sh test

# 查看容器状态
./scripts/connect-mongodb.sh status
```

## 故障排除

### 常见问题

**1. 端口占用**
```bash
# 检查端口占用情况
netstat -tulpn | grep :80
netstat -tulpn | grep :3000
netstat -tulpn | grep :27018

# 停止占用端口的服务或修改docker-compose.yml中的端口映射
```

**2. Docker服务未启动**
```bash
# 启动Docker服务
sudo systemctl start docker    # Linux
# 或启动Docker Desktop        # Windows/macOS
```

**3. 权限问题**
```bash
# Linux下添加用户到docker组
sudo usermod -aG docker $USER
# 重新登录或执行
newgrp docker
```

**4. 镜像导入失败**
```bash
# 检查镜像文件完整性
ls -la images/
# 手动导入单个镜像
docker load -i images/镜像文件名.tar
```

**5. 服务启动失败**
```bash
# 查看详细错误日志
docker-compose logs 服务名

# 检查容器状态
docker ps -a

# 重新创建容器
docker-compose down
docker-compose up -d --force-recreate
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api-server
docker-compose logs -f web-client
docker-compose logs -f mongodb

# 查看最近的日志
docker-compose logs --tail=100 api-server
```

### 数据备份

```bash
# 备份MongoDB数据
docker exec xkd-mongodb mongodump --db apiServer_xkdgcc --out /data/backup

# 从容器复制备份文件
docker cp xkd-mongodb:/data/backup ./mongodb-backup
```

## 技术架构

### 服务组件
- **MongoDB**: 数据存储服务
- **API Server**: Node.js + Restify 后台服务，使用PM2管理
- **Web Client**: React + Ant Design Pro 前端应用，使用Nginx部署

### 网络配置
- 所有服务运行在独立的Docker网络中
- 前端通过Nginx代理访问后台API
- 后台服务通过内部网络连接MongoDB

### 数据持久化
- MongoDB数据存储在Docker卷中
- 停止容器不会丢失数据
- 如需完全清理，使用 `docker-compose down --volumes`

## 联系支持

如遇到部署问题，请联系技术支持团队。

---

**西安科技大学**  
**高层次人才服务管理平台**
