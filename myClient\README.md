# 西安科技大学高层次人才服务管理平台

## 项目简介
本项目是基于 Ant Design Pro 开发的西安科技大学高层次人才服务管理平台，用于管理和服务高层次人才的综合性Web应用。

## 主要功能
- 人才信息管理
- 待遇兑现管理
- 成果管理（论文、项目、其他成果）
- 数据统计与分析
- 汇总导出功能
- 多语言支持（中文简体、中文繁体、英文）

## 技术栈
- **前端框架**: React 16.7.0
- **UI组件库**: Ant Design 3.17.0
- **状态管理**: Dva 2.4.1
- **构建工具**: UmiJS 2.7.3
- **图表库**: BizCharts 3.4.3
- **样式**: Less
- **国际化**: 内置多语言支持

## 环境依赖
- **Node.js**: 建议使用 16.x 版本

## 开发注意事项
### 参考文档
https://pro.ant.design/docs/getting-started-cn?tdsourcetag=s_pctim_aiomsg

### Chromium源码被墙问题
默认使用 npm install ，执行 install.js 会下载 Chromium 源码，由于被墙的原因，需要提前手动下载，
全局安装puppeteer，再把 Chromium 源码拷贝到全局 node_modules 中 puppeteer\.local-chromium路径下
```bash
npm install -g puppeteer --ignore-scripts
```
然后执行 npm install 进行编译

## 常见问题解决

### 项目启动报错解决方案

如果遇到项目启动失败的问题，通常是由于依赖版本冲突或缺少依赖包导致的。以下是完整的解决步骤：

#### 问题现象
- 执行 `npm start` 时提示 `'cross-env' 不是内部或外部命令`
- 依赖安装时出现 peer dependency 冲突错误
- React 版本兼容性问题

#### 解决步骤

1. **清理现有依赖（如果存在）**
   ```bash
   # 删除 node_modules 文件夹（如果存在）
   rm -rf node_modules

   # 删除 package-lock.json（如果存在）
   rm package-lock.json
   ```

2. **使用兼容模式安装依赖**
   ```bash
   # 使用 legacy-peer-deps 参数解决版本冲突
   npm install --legacy-peer-deps
   ```

3. **启动项目**
   ```bash
   npm start
   ```

#### 为什么使用 --legacy-peer-deps？
- 项目中的 `react-fittext@1.0.0` 依赖要求 React 15，但项目使用的是 React 16.7.0
- `--legacy-peer-deps` 参数告诉 npm 使用旧的依赖解析算法，忽略 peer dependency 版本冲突
- 这是一个临时解决方案，确保项目能够正常运行

#### 成功启动后的访问地址
- 本地访问：http://localhost:8000/
- 网络访问：http://[你的IP]:8000/

#### 注意事项
- 安装过程中可能会出现一些 deprecated 警告，这是正常现象
- 建议在后续版本中考虑升级相关依赖包
- 如果仍有问题，请检查 Node.js 版本是否符合要求

## 快速开始

### 安装依赖
```bash
# 推荐方式：使用 npm 并解决版本冲突
npm install --legacy-peer-deps
```

> **重要提示**：由于项目存在依赖版本冲突，强烈推荐使用 `npm install --legacy-peer-deps` 命令安装依赖。

### 开发环境启动
```bash
# 启动开发服务器
npm run start

# 启动开发服务器（测试环境API）
npm run start:test

# 启动开发服务器（带Mock数据）
npm run start:mock
```

### 构建部署
```bash
# 生产环境构建
npm run build

# 测试环境构建
npm run build:test
```

### 代码检查
```bash
# 运行ESLint检查
npm run lint

# 自动修复代码格式问题
npm run lint:fix
```

### 测试
```bash
# 运行所有测试
npm run test:all

# 运行组件测试
npm run test:component
```

## 项目结构
```
myClient/
├── src/
│   ├── components/     # 公共组件
│   ├── locales/       # 国际化文件
│   ├── models/        # Dva数据模型
│   ├── pages/         # 页面组件
│   └── defaultSettings.js  # 默认配置
├── config/            # 配置文件
├── docker/           # Docker配置
└── package.json      # 项目依赖
```

## 环境配置
项目支持多环境配置：
- **local**: 本地开发环境
- **dev**: 开发测试环境  
- **production**: 生产环境

## Docker部署
```bash
# 构建Docker镜像
npm run docker:build

# 启动Docker容器
npm run docker:dev
```

## 浏览器支持
- Chrome (推荐)
- Firefox
- Safari
- Edge
- IE 11+
