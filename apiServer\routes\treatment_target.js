/*
 * @Description: 待遇协议相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-23 20:00:26
 * @LastEditTime: 2019-05-06 11:31:24
 */

const userApi = require('../api/treatment_target');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createTr_t', validator.typeIsJson, userApi.saveTr_t);
	server.get('/tr_t_list', userApi.getList);
	server.post('/updateTr_t', validator.typeIsJson, userApi.updateTr_t);
	server.post('/delTr_t_list', validator.typeIsJson, userApi.delTr_t_list);
};