/*
 * @Description: 工具类
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-05-05 11:18:27
 * @LastEditTime: 2019-05-19 11:14:50
 */
const ejsexcel = require('ejsexcel');
const fs = require('fs');
const util = require("util");
const readFileAsync = util.promisify(fs.readFile);
const writeFileAsync = util.promisify(fs.writeFile);

module.exports = {
  /**
   * 根据num查询多个记录
   *
   * @param {Array} list
   * @param {String} num
   * @returns
   */
  findListByNum: (list, num) => {
    let returnList = [];
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.num === num) {
        returnList.push(item);
      }
    }
    return returnList;
  },
  /**
   * 根据num查询单个记录
   *
   * @param {Array} list
   * @param {String} num
   * @returns
   */
  findOneByNum: (list, num) => {
    for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.num === num) {
        return item;
      }
    }
    return {};
  },
  /**
   * 把数据库中存储的数组字符串转换为导出表格使用的按年份存储的数据
   *
   * @param {*} obj 所属对象
   * @param {*} key 属性名称.
   * @param {*} list 待转换数据
   * @param {*} years 年份范围
   * @param {*} years 时间排列方向，row 横向，col 纵向
   */
  convertListToYearValue: (obj, key, list, years, tableDir) => {
    const yearList = eval(years) instanceof Array ? eval(years) : [];
    const array = eval(list);
    const dir = tableDir || 'row';
    if(array && array instanceof Array){
      array.map(arr => {
        if(arr instanceof Array){
          const year = new Date(`${arr[0]}`).getFullYear();
          const val = arr[1];
          if (yearList.includes(`${year}`)){
            if(dir === 'row'){
              obj[`${key}&${year}`] = val;
            }else{
              obj[key] = obj[key] || [];
              obj[key].push([year, val]);
            }
          }
        }
      })
    }
  },
  /**
   * 转换日期格字符串
   *
   * @param {String} timeStr 待转换字符串
   * @param {Boolean} getNow 如果转换失败，是否以当前时间代替
   * @returns
   */
  getTime: (timeStr, getNow) => {
    let flag = false;
    if (timeStr && typeof timeStr !== 'undefined' && timeStr !== '') {
      flag = true;
    }

    if (flag) {
      return new Date(timeStr).toLocaleString();
    } else if (getNow) {
      return new Date().toLocaleString();
    } else {
      return null;
    }
  },

  /**
   * 导出excel文件
   *
   * @param {Object} data 数据源
   * @param {String} complate 模板路径
   * @param {String} saveDir 保存路径
   */
  exportToExcel: async (data, complate, saveDir) => {
    //获得Excel模板的buffer对象
    const exlBuf = await readFileAsync(complate);
    //数据源  
    // const data = [[{ "dpt_des": "开发部", "doc_dt": "2013-09-09", "doc": "a001" }], [{ "pt": "pt1", "des": "des1", "due_dt": "2013-08-07", "des2": "2013-12-07" }, { "pt": "pt1", "des": "des1", "due_dt": "2013-09-14", "des2": "des21" }]];
    //用数据源(对象)data渲染Excel模板
    const exlBuf2 = await ejsexcel.renderExcel(exlBuf, data);
    await writeFileAsync(saveDir, exlBuf2);
    console.log(`生成${saveDir}`);
  }
}