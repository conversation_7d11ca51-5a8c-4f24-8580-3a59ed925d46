/*
 * @Description: 其他目标相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-27 15:27:19
 * @LastEditTime: 2019-05-06 11:44:52
 */

const userApi = require('../api/other_target');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createOt_t', validator.typeIsJson, userApi.saveOt_t);
	server.get('/ot_t_list', userApi.getList);
	server.post('/updateOt_t', validator.typeIsJson, userApi.updateOt_t);
	server.post('/delOt_t_list', validator.typeIsJson, userApi.delOt_t_list);
};