/**
 * 项目目标表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');
const util = require('../util/util');

const ProjectTargetSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {	// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		qj_date: {	// 青基获批时限
			type: Date
		},
		ms_date: {	// 面上获批时限
			type: Date
		},
		jfze: {	// 立项批准金额
			type: Number
		},
		remark: {	// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

ProjectTargetSchema.plugin(timestamps);

const ProjectTarget = mongoose.model('project_target', ProjectTargetSchema, 'project_targets');

class ProjectTargetUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ProjectTargetUtil
	 */
	assign(source) {
		let target = {};
		if(!source || typeof source === 'undefined'){
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		target.qj_date = util.getTime(source.qj_date);	// 青基获批时限
		target.ms_date = util.getTime(source.ms_date);	// 面上获批时限
		typeof source.jfze !== 'undefined' && (target.jfze = source.jfze);		// 立项批准金额
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}

	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ProjectTargetUtil
	 */
	assign_export(source) {
		let target = {};
		if(!source || typeof source === 'undefined'){
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		const qj_date = util.getTime(source.qj_date);
		target.qj_date = qj_date ? `${new Date(qj_date).getFullYear()}年${new Date(qj_date).getMonth() + 1}月` : ``;	// 青基获批时限
		const ms_date = util.getTime(source.ms_date);
		target.ms_date = ms_date ? `${new Date(ms_date).getFullYear()}年${new Date(ms_date).getMonth() + 1}月` : ``;	// 面上获批时限
		typeof source.jfze !== 'undefined' && (target.jfze = source.jfze);		// 立项批准金额
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const projectTargetUtil = new ProjectTargetUtil();

module.exports = { ProjectTarget, projectTargetUtil };    