#!/bin/bash

# GCC-XKD Startup Script (Linux/macOS)
echo "Starting GCC-XKD services..."

# Check configuration file
if [ ! -f "docker-compose.yml" ]; then
    if [ -f "config/docker-compose.yml" ]; then
        echo "Copying configuration file..."
        cp config/docker-compose.yml .
    else
        echo "docker-compose.yml configuration file not found"
        exit 1
    fi
fi

# Start services
echo "Starting Docker services..."
docker-compose up -d

if [ $? -eq 0 ]; then
    echo "Services started successfully"
    echo ""
    echo "Access URLs:"
    echo "- Frontend: http://localhost"
    echo "- API: http://localhost:3000"
    echo "- MongoDB: localhost:27018"
    echo ""
    echo "Check status: docker-compose ps"
    echo "View logs: docker-compose logs -f"
else
    echo "Failed to start services"
    echo "Please check logs: docker-compose logs"
fi
