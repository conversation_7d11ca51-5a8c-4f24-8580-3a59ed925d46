/*
 * @Description: 定义接口
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-30 12:01:02
 * @LastEditTime: 2019-06-20 13:41:30
 */
const path = require('path');
// const fs = require('fs');

const dictionary = require('../dictionary');
const util = require('../util/util');
const fileManager = require('../util/file_manager');

const uploadPath = dictionary.upload.savePath;
const exportPath = dictionary.export.savePath;

/**
 * Model Schema
 */
const { User, userUtil } = require('../models/user');
const { TreatmentTarget, treatmentTargetUtil } = require('../models/treatment_target');
const { TreatmentResult, treatmentResultUtil } = require('../models/treatment_result');
const { ThesisTarget, thesisTargetUtil } = require('../models/thesis_target');
const { ThesisResult, thesisResultUtil } = require('../models/thesis_result');
const { ProjectTarget, projectTargetUtil } = require('../models/project_target');
const { ProjectResult, projectResultUtil } = require('../models/project_result');
const { OtherTarget, otherTargetUtil } = require('../models/other_target');
const { OtherResult, otherResultUtil } = require('../models/other_result');

/**
 * 登录
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const doLogin = (req, res, next) => {
  console.log('api doLogin')

  let data = req.body || {};
  let info = Object.assign({ num: '', pwd: '' }, data);
  User.findOne(info).then(user => {
    if (user) {
      res.send({ "status": "ok", "type": "account", "currentAuthority": user.level ? "user" : "admin", "num": user.num })
    } else {
      res.send({ "status": "error", "type": "account", "currentAuthority": "guest" })
    }
  }).catch(err => {
    console.error(err);
    return next(err)
  })
}

/**
 * 按年读取兑现信息保存到指定对象中
 *
 * @param {Object} obj 待写入对象
 * @param {String} key 写入的key
 * @param {String} year 写入的年份
 * @param {String} val 写入的值
 * @param {Array} years 读取年份范围
 */
const readResultInfoByYear = (obj, key, year, val, years) => {
  if (year !== '' && years.includes(`${year}`)) {
    obj[year] = obj[year] || {};
    obj[year][key] = val;
  }
}

/**
 * 根据人员代码查询人员基本情况
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const queryBaseInfo = (req, res, next) => {
  console.log('api queryBaseInfo')
  let reqData = req.body || {};

  const { num, years } = reqData;
  Promise.all([
    User.findOne({ num: num }),
    TreatmentTarget.findOne({ num: num }),
    TreatmentResult.findOne({ num: num }),
    ThesisTarget.findOne({ num: num }),
    ThesisResult.find({ num: num }),
    ProjectTarget.findOne({ num: num }),
    ProjectResult.find({ num: num }),
    OtherTarget.findOne({ num: num }),
    OtherResult.findOne({ num: num })
  ]).then(list => {
    const user = userUtil.assign_export(list[0]);
    const treatment_t = treatmentTargetUtil.assign(list[1]);
    const treatment_r = treatmentResultUtil.assign_export(list[2], years, 'col');
    const thesis_t = thesisTargetUtil.assign(list[3]);
    const thesis_r_list = list[4];
    const project_t = projectTargetUtil.assign_export(list[5]);
    const project_r_list = list[6];
    const other_t = otherTargetUtil.assign(list[7]);
    const other_r = otherResultUtil.assign_export(list[8], years, 'col');

    let returnInfo = {
      ...user,
      ...treatment_t,
      ...thesis_t,
      ...project_t,
      ...other_t
    };

    // 处理成果信息
    if (user.level === dictionary.userLevel.default) {
      // 住 房
      if (!!treatment_r.housing_date__r) {
        let year = treatment_r.housing_date__r.split("年")[0];
        let key = `housing`;
        let val = treatment_r.housing__r;
        readResultInfoByYear(returnInfo, key, year, val, years);
      }

      // 过度住房
      if (!!treatment_r.makeshift_house_date__r) {
        year = treatment_r.makeshift_house_date__r.split("年")[0];
        key = `makeshift_house`;
        val = treatment_r.makeshift_house__r;
        readResultInfoByYear(returnInfo, key, year, val, years);
      }

      // 安家费
      if (treatment_r.settling_in_allowance__r && treatment_r.settling_in_allowance__r.length) {
        for (let i = 0; i < treatment_r.settling_in_allowance__r.length; i++) {
          const current = treatment_r.settling_in_allowance__r[i];
          year = current[0];
          key = 'settling_in_allowance';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }

      // 科研经费
      if (treatment_r.scientific_research_funds__r && treatment_r.scientific_research_funds__r.length) {
        for (let i = 0; i < treatment_r.scientific_research_funds__r.length; i++) {
          const current = treatment_r.scientific_research_funds__r[i];
          year = current[0];
          key = 'scientific_research_funds';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }

      // 实验经费
      if (treatment_r.lab_construction_fee__r && treatment_r.lab_construction_fee__r.length) {
        for (let i = 0; i < treatment_r.lab_construction_fee__r.length; i++) {
          const current = treatment_r.lab_construction_fee__r[i];
          year = current[0];
          key = 'lab_construction_fee';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }

      // 科研用房
      if (!!treatment_r.srh_date__r) {
        year = treatment_r.srh_date__r.split("年")[0];
        key = `scientific_research_house`;
        val = treatment_r.scientific_research_house__r;
        readResultInfoByYear(returnInfo, key, year, val, years);
      }

      // 购房补贴
      if (treatment_r.housing_subsidies__r && treatment_r.housing_subsidies__r.length) {
        for (let i = 0; i < treatment_r.housing_subsidies__r.length; i++) {
          const current = treatment_r.housing_subsidies__r[i];
          year = current[0];
          key = 'housing_subsidies';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }

      // 职称特评
      if (!!treatment_r.title_tp_date__r) {
        year = treatment_r.title_tp_date__r.split("年")[0];
        key = `title_tp`;
        val = treatment_r.title_tp__r;
        readResultInfoByYear(returnInfo, key, year, val, years);
      }

      // 配偶工作
      if (!!treatment_r.spouse_work_date__r) {
        year = treatment_r.spouse_work_date__r.split("年")[0];
        key = `spouse_work`;
        val = treatment_r.spouse_work__r;
        readResultInfoByYear(returnInfo, key, year, val, years);
      }

      // 论文
      if (thesis_r_list && thesis_r_list.length) {
        let thesisSummary = {};
        for (let i = 0; i < thesis_r_list.length; i++) {
          const thesis = thesis_r_list[i];
          const year = thesis.tj_year;
          thesisSummary[year] = thesisSummary[year] || { sum: 0, sci: 0, sci1: 0, sci2: 0 };
          if (thesis.lwlx_custom === dictionary.thesisType.sci) {
            thesisSummary[year].sci++;
            thesisSummary[year].sum++;
          } else if (thesis.lwlx_custom === dictionary.thesisType.sci1) {
            thesisSummary[year].sci1++;
            thesisSummary[year].sum++;
          } else if (thesis.lwlx_custom === dictionary.thesisType.sci2) {
            thesisSummary[year].sci2++;
            thesisSummary[year].sum++;
          }
        }
        for (const k in thesisSummary) {
          if (thesisSummary.hasOwnProperty(k)) {
            const item = thesisSummary[k];
            const year = k;
            let key = 'sum';
            let val = item.sum;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
            key = 'sci';
            val = item.sci;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
            key = 'sci1';
            val = item.sci1;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
            key = 'sci2';
            val = item.sci2;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
          }
        }
      }

      // 项目 
      if (project_r_list && project_r_list.length) {
        let projectSummary = {};
        for (let i = 0; i < project_r_list.length; i++) {
          const project = project_r_list[i];
          const year = project.lxnd;
          projectSummary[year] = projectSummary[year] || { qj: 0, ms: 0, lxpzje: 0 };
          if (project.xmlx.indexOf('面上') >= 0) {
            projectSummary[year].ms++;
            projectSummary[year].lxpzje += parseFloat(project.lxpzje);
          } else if (project.xmlx.indexOf('青年科学基金') >= 0) {
            projectSummary[year].qj++;
            projectSummary[year].lxpzje += parseFloat(project.lxpzje);
          }
        }
        for (const k in projectSummary) {
          if (projectSummary.hasOwnProperty(k)) {
            const item = projectSummary[k];
            const year = k;
            let key = 'ms';
            let val = item.ms;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
            key = 'qj';
            val = item.qj;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
            key = 'lxpzje';
            val = item.lxpzje;
            val && readResultInfoByYear(returnInfo, key, year, val, years);
          }
        }
      }

      // 申报人才称号
      if (other_r.sbrcch && treatment_r.sbrcch.length) {
        for (let i = 0; i < treatment_r.sbrcch.length; i++) {
          const current = treatment_r.sbrcch[i];
          year = current[0];
          key = 'sbrcch';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }

      // 推荐人才
      if (other_r.tjrc && treatment_r.tjrc.length) {
        for (let i = 0; i < treatment_r.tjrc.length; i++) {
          const current = treatment_r.tjrc[i];
          year = current[0];
          key = 'tjrc';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }

      // 获奖
      if (other_r.hjqk && treatment_r.hjqk.length) {
        for (let i = 0; i < treatment_r.hjqk.length; i++) {
          const current = treatment_r.hjqk[i];
          year = current[0];
          key = 'hjqk';
          val = current[1];
          readResultInfoByYear(returnInfo, key, year, val, years);
        }
      }
    }

    res.send({
      status: 'ok',
      info: returnInfo
    });
  }).catch(err => {
    console.error(err)
    res.send({
      status: 'error',
      message: err.message
    });
  })
}

/**
 * 人员导出基本情况表
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
const exportBaseInfo = (req, res, next) => {
  console.log('api exportBaseInfo')
  let reqData = req.body || {};

  let { currentData, years } = reqData;
  currentData.years = years;
  const time = new Date();
  const fileTag = `${time.getFullYear()}-${time.getMonth() + 1}-${time.getDate()}_${time.getTime() % 86400000}`;
  const templatePath = path.resolve(__dirname, '../export_template/report.xlsx');
  const fileName = `基本情况表(${currentData.name})-${fileTag}.xlsx`;
  const savePath = `${exportPath}/${fileName}`;
  // const savePath = path.resolve(__dirname, `../export_target/${fileName}`);
  util.exportToExcel(currentData, templatePath, savePath).then(() => {
    res.send({ 'status': 'ok', 'fileName': fileName });
  }).catch(err => {
    console.error(err);
    res.send({ 'status': 'error', 'message': err.message });
  });
}

/**
 * 导出
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @returns
 */
const exportToExcel = (req, res, next) => {
  console.log('api exportToExcel')
  let reqData = req.body || {};

  const time = new Date();
  const fileTag = `${time.getFullYear()}-${time.getMonth() + 1}-${time.getDate()}_${time.getTime() % 86400000}`;
  let fileName;

  // 测试数据
  // const testData = {
  //   data: [[{ "dpt_des": "开发部", "doc_dt": "2013-09-09", "doc": "a001" }], [{ "pt": "pt1", "des": "des1", "due_dt": "2013-08-07", "des2": "2013-12-07" }, { "pt": "pt1", "des": "des1", "due_dt": "2013-09-14", "des2": "des21" }]],
  //   template: path.resolve(__dirname, '../export_template/test.xlsx'),
  //   saveDir: path.resolve(__dirname, '../export_target/test.xlsx')
  // }

  const summary_years = reqData.years || [];
  switch (reqData.type) {
    case 'treatment':
      queryExport_treatment(reqData.cols, summary_years).then(results => {
        let cols = [];
        for (let i = 0; i < reqData.cols.length; i++) {
          const col = reqData.cols[i];
          if (['settling_in_allowance__r', 'scientific_research_funds__r', 'lab_construction_fee__r', 'housing_subsidies__r'].includes(col)) {
            for (let j = 0; j < summary_years.length; j++) {
              const year = summary_years[j];
              cols.push(`${col}&${year}`);
            }
          } else {
            cols.push(col);
          }
        }
        // console.log(cols)
        // console.dir(results[0])
        const data = {
          dictionary: dictionary.export.base,
          cols: cols,
          list: results
        };
        const templatePath = path.resolve(__dirname, '../export_template/treatment.xlsx');
        fileName = `人员基本信息-${fileTag}.xlsx`;
        const savePath = `${exportPath}/${fileName}`;
        // const savePath = path.resolve(__dirname, `../export_target/${fileName}`);
        return util.exportToExcel(data, templatePath, savePath);
      }).then(() => {
        res.send({ 'status': 'ok', 'fileName': fileName });
      }).catch(err => {
        console.error(err);
        res.send({ 'status': 'error', 'message': err.message });
      });
      break;
    case 'thesis':
      queryExport_thesis(reqData.cols).then(results => {
        const data = {
          userDictionary: dictionary.export.user,
          dictionary: dictionary.export.thesis_r,
          cols: reqData.cols,
          list: results
        };
        const templatePath = path.resolve(__dirname, '../export_template/thesis.xlsx');
        fileName = `论文成果统计-${fileTag}.xlsx`;
        const savePath = `${exportPath}/${fileName}`;
        // const savePath = path.resolve(__dirname, `../export_target/${fileName}`);
        return util.exportToExcel(data, templatePath, savePath);
      }).then(() => {
        res.send({ 'status': 'ok', 'fileName': fileName });
      }).catch(err => {
        console.error(err);
        res.send({ 'status': 'error', 'message': err.message });
      });
      break;
    case 'project':
      queryExport_project(reqData.cols).then(results => {
        const data = {
          userDictionary: dictionary.export.user,
          dictionary: dictionary.export.project_r,
          cols: reqData.cols,
          list: results
        };
        const templatePath = path.resolve(__dirname, '../export_template/project.xlsx');
        fileName = `项目成果统计-${fileTag}.xlsx`;
        const savePath = `${exportPath}/${fileName}`;
        // const savePath = path.resolve(__dirname, `../export_target/${fileName}`);
        return util.exportToExcel(data, templatePath, savePath);
      }).then(() => {
        res.send({ 'status': 'ok', 'fileName': fileName });
      }).catch(err => {
        console.error(err);
        res.send({ 'status': 'error', 'message': err.message });
      });
      break;
    case 'other':
      queryExport_other(reqData.cols, summary_years).then(results => {
        let cols = [];
        for (let i = 0; i < reqData.cols.length; i++) {
          const col = reqData.cols[i];
          if (['xmjf', 'sbrcch', 'tjrc'].includes(col)) {
            for (let j = 0; j < summary_years.length; j++) {
              const year = summary_years[j];
              cols.push(`${col}&${year}`);
            }
          } else {
            cols.push(col);
          }
        }
        const data = {
          userDictionary: dictionary.export.user,
          dictionary: dictionary.export.other_r,
          cols: reqData.cols,
          list: results
        };
        const templatePath = path.resolve(__dirname, '../export_template/other.xlsx');
        fileName = `其他成果统计-${fileTag}.xlsx`;
        const savePath = `${exportPath}/${fileName}`;
        // const savePath = path.resolve(__dirname, `../export_target/${fileName}`);
        return util.exportToExcel(data, templatePath, savePath);
      }).then(() => {
        res.send({ 'status': 'ok', 'fileName': fileName });
      }).catch(err => {
        console.error(err);
        res.send({ 'status': 'error', 'message': err.message });
      });
      break;
    default:
      res.send({ 'status': 'error', 'message': '导出类型错误。' });
  }
}

/**
 * 查询人员基本信息导出信息
 *
 * @param {Array} cols 表头
 * @param {Array} years 统计年限
 * @returns
 */
const queryExport_treatment = (cols, years) => {
  let resultCols = [];
  cols.map(col => {
    if (col.indexOf("__") > 0) {
      resultCols.push(col.split("__")[0]);
    }
  });
  return Promise.all([
    User.find({ level: 1 }, `num ${cols.join(' ')}`),
    TreatmentTarget.find({}, `num ${cols.join(' ')}`),
    TreatmentResult.find({}, `num ${resultCols.join(' ')}`),
    ThesisTarget.find({}, `num ${cols.join(' ')}`),
    ProjectTarget.find({}, `num ${cols.join(' ')}`),
    OtherTarget.find({}, `num ${cols.join(' ')}`),
  ]).then(list => {
    const users = list[0];
    const treatment_ts = list[1];
    const treatment_rs = list[2];
    const thesis_ts = list[3];
    const project_ts = list[4];
    const other_ts = list[5];
    let returnList = [];
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      returnList.push(
        Object.assign({},
          userUtil.assign_export(user),
          treatmentTargetUtil.assign(util.findOneByNum(treatment_ts, user.num)),
          treatmentResultUtil.assign_export(util.findOneByNum(treatment_rs, user.num), years, 'row'),
          thesisTargetUtil.assign(util.findOneByNum(thesis_ts, user.num)),
          projectTargetUtil.assign_export(util.findOneByNum(project_ts, user.num)),
          otherTargetUtil.assign(util.findOneByNum(other_ts, user.num))
        )
      );
    }
    return Promise.resolve(returnList);
  })
}

/**
 * 查询论文成果导出信息
 *
 * @param {Array} cols 表头
 * @returns
 */
const queryExport_thesis = cols => {
  return ThesisResult.find({}, cols.join(' ')).populate('user_id');
}

/**
 * 查询项目成果导出信息
 *
 * @param {Array} cols 表头
 * @returns
 */
const queryExport_project = cols => {
  return ProjectResult.find({}, cols.join(' ')).populate('user_id');
}

/**
 * 查其他成果文导出信息
 *
 * @param {Array} cols 表头
 * @param {Array} years 统计年限
 * @returns
 */
const queryExport_other = (cols, years) => {
  return OtherResult.find({}, cols.join(' ')).populate('user_id').then(results => {
    const returnList = results.map(result => {
      let r = otherResultUtil.assign_export(result, years, 'row');
      r.name = result.user_id.name;
      return r;
    });
    return new Promise.resolve(returnList);
  });
}

const upload = (req, res, next) => {
  console.log('api upload')
  // Check if upload failed or was aborted
  if (req.jfum.error) {
    // req.jfum.error
  } else {
    // Here are the uploaded files
    for (var i = 0; i < req.jfum.files.length; i++) {
      var file = req.jfum.files[i];

      // Check if file has errors
      if (file.errors.length > 0) {
        for (var j = 0; i < file.errors.length; i++) {
          let message = file.errors[j].message;
          console.log(message)
          res.send({
            status: 'error',
            message: message
          });
          return false;
        }
      } else {
        const tempPath = file.path.replace(/\\/g, '/');
        const tempName = tempPath.substring(tempPath.lastIndexOf('\/')+1);
        fileManager.moveFile(file.path, `${uploadPath}/${tempName}`, ()=>{
          res.send({
            status: 'ok',
            name: file.name,
            url: `/server/api/download/${tempName}`,
            path: `${uploadPath}/${tempName}`
          });
        }, err=>{
            res.send({
              status: 'error',
              message: err.code
            });
        }, false);      
      }
    }
  }
}

module.exports = {
  doLogin: doLogin,
  queryBaseInfo: queryBaseInfo,
  exportBaseInfo: exportBaseInfo,
  exportToExcel: exportToExcel,
  upload: upload
}