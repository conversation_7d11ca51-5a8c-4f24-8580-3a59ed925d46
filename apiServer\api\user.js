/*
 * @Description: 用户相关接口
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-05-01 12:51:06
 * @LastEditTime: 2019-05-21 09:15:52
 */

/**
 * Module Dependencies
 */
const errors = require('restify-errors');
const dictionary = require('../dictionary');

/**
 * Model Schema
 */
const { User, userUtil } = require('../models/user');
const { TreatmentTarget } = require('../models/treatment_target');
const { TreatmentResult } = require('../models/treatment_result');
const { ThesisTarget } = require('../models/thesis_target');
const { ThesisResult } = require('../models/thesis_result');
const { ProjectTarget } = require('../models/project_target');
const { ProjectResult } = require('../models/project_result');
const { OtherTarget } = require('../models/other_target');
const { OtherResult } = require('../models/other_result');

module.exports = {

  /**
   * 新建用户
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
  saveUser: (req, res, next) => {
    console.log('%s user saveUser', new Date().toLocaleString());
    let data = req.body || {};
    console.log('name: %s', data.name)

    User.findOne({ num: data.num }).then(result => {
      if (result) {
        return Promise.reject({ "status": "error", "message": "用户已存在。" });
      } else {
        return userUtil.assign(data, true); // 过滤无效值
      }
    }).then(info => {
      // 入库
      let user = new User(info);
      return user.save();
    }).then(result => {
      res.send({ "result": result, "status": "ok" });
    }).catch(err => {
      console.error(err);
      if (err.status === "error") {
        res.send(err);
      } else if (err.code == 11000) {
        let name = err.message.split("\"")[1];
        return next(new errors.InvalidVersionError("用户字段重复： " + name + "。"));
      } else {
        return next(err);
      }
    });
  },

  /**
   * 获取所有用户
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
  getList: (req, res, next) => {
    console.log('user getList');
    let {
      name,
      sorter,
      pageSize = 1000,
      currentPage = 1,
      showListMode = dictionary.userLevel.default,
      getTreatment_t = false,
      getThesis_t = false,
      getProject_t = false,
      getOther_t = false
    } = req.params;

    // 筛选条件
    let queryJson = {};
    if (name) {
      queryJson = { "$or": [{ num: new RegExp(name, 'i') }, { name: new RegExp(name, 'i') }, { showListMode: showListMode }] };
      currentPage = 1;
    }
    queryJson.level = showListMode;

    // 排序条件
    let sortJson = { level: 1, num: 1 };
    if (sorter) {
      const s = sorter.split('_');
      const sortName = s[0];
      const dir = s[1] === 'descend' ? -1 : 1;
      sortJson = {};
      sortJson[sortName] = dir;
    }

    let total = 0;
    Promise.all([
      User.find(queryJson).countDocuments(),
      User.find(queryJson, null, {
        sort: sortJson,
        limit: parseInt(pageSize),
        skip: (currentPage - 1) * pageSize
      })
    ]).then(list => {
      total = list[0];
      let results = list[1];

      let returnList = [];
      if (results && results.length) {
        returnList = results.map(result => {
          return userUtil.assign(result, false, getTreatment_t, getThesis_t, getProject_t, getOther_t);
        });
      }
      return Promise.all(returnList);
    }).then(list => {
      res.send({
        list: list,
        pagination: {
          currentPage: parseInt(currentPage),
          pageSize: parseInt(pageSize),
          total: total,
        },
      });
    }).catch(err => {
      console.error(err);
      next(err);
    });
  },

  /**
   * 根据人员编号获取用户信息
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
  getUserByNum: (req, res, next) => {
    console.log('user getUserByNum');
    const num = req.params.user_num;
    User.findOne({ num, num }).then(result => {
      res.send(result);
    }).catch(err => {
      console.error(err);
      next(err);
    });
  },

  /**
   * 更新用户信息
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
  updateUserInfo: (req, res, next) => {
    console.log('%s user updateUserInfo', new Date().toLocaleString());
    let data = req.body || {};
    console.log('num: %s', data.num)

    let user;
    User.findOne({ num: data.num }).then(result => {
      if (!result) {
        return Promise.reject({ "status": "error", "message": "人员代码无效。" });
      }
      user = result;
      return userUtil.assign(data, true);   // 过滤无效值   
    }).then(info => {
      // 入库
      return user.updateOne(info);
    }).then(() => {
      // 如果权限发生变化，删除关联表内容
      switch (user.level) {
        case dictionary.userLevel.administrator:
          return Promise.all([
            TreatmentTarget.deleteOne({ num: data.num }),
            TreatmentResult.deleteMany({ num: data.num }),
            ThesisTarget.deleteOne({ num: data.num }),
            ThesisResult.deleteMany({ num: data.num }),
            ProjectTarget.deleteOne({ num: data.num }),
            ProjectResult.deleteMany({ num: data.num }),
            OtherTarget.deleteOne({ num: data.num }),
            OtherResult.deleteMany({ num: data.num })
          ]);
        case dictionary.userLevel.default:
      }
    }).then(() => {
      res.send({ "status": "ok" });
    }).catch(err => {
      console.error(err);
      if (err.status === "error") {
        res.send(err);
      } else {
        next(err);
      }
    });
  },

  /**
   * 删除指定用户
   *
   * @param {*} req
   * @param {*} res
   * @param {*} next
   */
  deleteUsers: (req, res, next) => {
    console.log('%s user deleteUsers', new Date().toLocaleString());
    let data = req.body || {};
    let { numList } = data;
    console.log('numList: %s', numList)

    Promise.all([
      // UserInfo.deleteMany({ num: { $in: numList } }),
      TreatmentTarget.deleteMany({ num: { $in: numList } }),
      TreatmentResult.deleteMany({ num: { $in: numList } }),
      ThesisTarget.deleteMany({ num: { $in: numList } }),
      ThesisResult.deleteMany({ num: { $in: numList } }),
      ProjectTarget.deleteMany({ num: { $in: numList } }),
      ProjectResult.deleteMany({ num: { $in: numList } }),
      OtherTarget.deleteMany({ num: { $in: numList } }),
      OtherResult.deleteMany({ num: { $in: numList } })
    ]).then(() => {
      // 用户
      return User.deleteMany({ num: { $in: numList } });
    }).then(() => {
      res.send({ "status": "ok" });
    }).catch(err => {
      console.error(err);
      next(err);
    });
  }
}