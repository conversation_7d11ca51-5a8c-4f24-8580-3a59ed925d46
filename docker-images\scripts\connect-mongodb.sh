#!/bin/bash

# MongoDB Connection Tool for GCC-XKD
# 用于连接Docker部署的MongoDB数据库

ACTION="${1:-connect}"
TARGET="${2:-docker}"

echo -e "\033[32mMongoDB Connection Tool\033[0m"
echo -e "\033[32m=======================\033[0m"

show_usage() {
    echo ""
    echo -e "\033[33mUsage:\033[0m"
    echo -e "\033[37m  ./connect-mongodb.sh [action] [target]\033[0m"
    echo ""
    echo -e "\033[33mActions:\033[0m"
    echo -e "\033[37m  connect  - Connect to MongoDB shell (default)\033[0m"
    echo -e "\033[37m  test     - Test MongoDB connection\033[0m"
    echo -e "\033[37m  status   - Show MongoDB container status\033[0m"
    echo ""
    echo -e "\033[33mTargets:\033[0m"
    echo -e "\033[37m  docker   - Docker MongoDB (port 27018, default)\033[0m"
    echo -e "\033[37m  local    - Local MongoDB (port 27017)\033[0m"
    echo ""
    echo -e "\033[33mExamples:\033[0m"
    echo -e "\033[36m  ./connect-mongodb.sh\033[0m"
    echo -e "\033[36m  ./connect-mongodb.sh connect docker\033[0m"
    echo -e "\033[36m  ./connect-mongodb.sh test docker\033[0m"
    echo -e "\033[36m  ./connect-mongodb.sh status\033[0m"
}

test_mongo_connection() {
    local connection_string="$1"
    
    echo -e "\033[33mTesting connection: $connection_string\033[0m"
    
    if [ "$TARGET" = "docker" ]; then
        # Use docker exec to test connection
        if docker exec xkd-mongodb mongo --eval "db.runCommand('ping')" --quiet &>/dev/null; then
            echo -e "\033[32mMongoDB connection successful\033[0m"
            return 0
        else
            echo -e "\033[31m✗ MongoDB connection failed\033[0m"
            return 1
        fi
    else
        # For local MongoDB, try to connect directly
        if mongo "$connection_string" --eval "db.runCommand('ping')" --quiet &>/dev/null; then
            echo -e "\033[32mMongoDB connection successful\033[0m"
            return 0
        else
            echo -e "\033[31m✗ MongoDB connection failed\033[0m"
            return 1
        fi
    fi
}

connect_mongodb() {
    local connection_string="$1"
    
    echo -e "\033[33mConnecting to MongoDB: $connection_string\033[0m"
    echo -e "\033[36mType 'exit' to quit MongoDB shell\033[0m"
    echo ""
    
    if [ "$TARGET" = "docker" ]; then
        # Connect via docker exec
        docker exec -it xkd-mongodb mongo apiServer_xkdgcc
    else
        # Connect directly to local MongoDB
        mongo "$connection_string"
    fi
}

show_container_status() {
    echo -e "\033[33mDocker container status:\033[0m"
    
    if docker ps -a --filter "name=xkd-mongodb" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q xkd-mongodb; then
        docker ps -a --filter "name=xkd-mongodb" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        echo -e "\033[31mNo MongoDB containers found\033[0m"
    fi
}

# Determine connection parameters
case "$TARGET" in
    "docker")
        PORT="27018"
        CONNECTION_STRING="mongodb://localhost:27018/apiServer_xkdgcc"
        echo -e "\033[36mTarget: Docker MongoDB (port $PORT)\033[0m"
        ;;
    "local")
        PORT="27017"
        CONNECTION_STRING="mongodb://localhost:27017/apiServer_xkdgcc"
        echo -e "\033[36mTarget: Local MongoDB (port $PORT)\033[0m"
        ;;
    *)
        echo -e "\033[31mUnknown target: $TARGET\033[0m"
        show_usage
        exit 1
        ;;
esac

echo ""

# Execute action
case "$ACTION" in
    "connect")
        if [ "$TARGET" = "docker" ]; then
            # Check if container is running
            if ! docker ps --filter "name=xkd-mongodb" --format "{{.Status}}" | grep -q "Up"; then
                echo -e "\033[31mMongoDB container is not running\033[0m"
                echo -e "\033[33mPlease start the services first: docker-compose up -d\033[0m"
                exit 1
            fi
        fi
        
        connect_mongodb "$CONNECTION_STRING"
        ;;
    "test")
        test_mongo_connection "$CONNECTION_STRING"
        ;;
    "status")
        show_container_status
        ;;
    "help")
        show_usage
        ;;
    *)
        echo -e "\033[31mUnknown action: $ACTION\033[0m"
        show_usage
        exit 1
        ;;
esac

echo ""
