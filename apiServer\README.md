# 西科大高层次人才管理系统 API 服务器

## 项目简介

本项目是西安科技大学高层次人才管理系统的后端API服务器，用于管理引进人才的目标设定、成果跟踪和待遇兑现等业务功能。

## 技术栈

- **Node.js** - 运行环境
- **Restify** - REST API框架
- **MongoDB** - 数据库
- **Mongoose** - MongoDB对象建模工具
- **EJS Excel** - Excel文件生成

## 环境要求

- **Node.js**: = 16
- **MongoDB**: >= 3.6.0

## 安装与运行

### 1. 克隆项目
```bash
git clone <repository-url>
cd apiServer
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
创建 `.env` 文件或设置以下环境变量：
```bash
NODE_ENV=development
PORT=3000
BASE_URL=http://localhost:3000
MONGODB_URI=mongodb://localhost:27017/apiServer_xkdgcc
```

### 4. 启动MongoDB服务
确保MongoDB服务正在运行

### 5. 启动应用
```bash
npm start
```

服务器将在 `http://localhost:3000` 启动

## 核心功能模块

### 目标管理
- **待遇协议** - 安家费、科研经费、住房等待遇协议管理
- **论文目标** - SCI论文发表目标设定
- **项目目标** - 科研项目申请目标管理
- **其他目标** - 人才称号申报、获奖等目标

### 成果管理
- **待遇兑现** - 各项待遇实际兑现情况记录
- **论文成果** - 已发表论文成果管理
- **项目成果** - 已获批科研项目记录
- **其他成果** - 人才称号、奖项等成果记录

### 支撑功能
- **用户管理** - 系统用户权限管理
- **支撑材料** - 相关证明文档管理
- **数据导出** - Excel格式报表导出
- **文件管理** - 文件上传下载功能

## API接口

### CORS配置
支持以下域名的跨域请求：
- `http://localhost:8000`
- `http://*************:8000`

### 主要路由
- `/api/*` - 通用业务接口
- `/user/*` - 用户管理
- `/treatment_target/*` - 待遇协议
- `/thesis_target/*` - 论文目标
- `/project_target/*` - 项目目标
- `/other_target/*` - 其他目标
- `/treatment_result/*` - 待遇兑现
- `/thesis_result/*` - 论文成果
- `/project_result/*` - 项目成果
- `/other_result/*` - 其他成果
- `/supporting_document/*` - 支撑材料

## 项目结构

```
apiServer/
├── config.js              # 配置文件
├── index.js               # 应用入口
├── package.json           # 项目依赖
├── routes/                # 路由模块
├── models/                # 数据模型
├── util/                  # 工具函数
└── uploads/               # 文件上传目录
```

## 开发说明

### 数据库初始化
系统启动时会自动执行：
- 数据库初始化 (`util/init_db`)
- 文件夹初始化 (`util/init_directory`)

### 中间件配置
- JSON请求体解析
- 查询参数解析
- CORS跨域支持
- 完整响应头设置

## 作者

**zpl** - 项目开发者

## 许可证

ISC License

