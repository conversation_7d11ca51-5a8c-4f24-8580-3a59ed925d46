/*
 * @Description: 论文成果相关路由
 * @Author: zpl
 * @LastEditors: zpl
 * @Date: 2019-04-27 17:26:10
 * @LastEditTime: 2019-05-08 12:48:41
 */

/**
 * Module Dependencies
 */
const api = require('../api/thesis_result');
const validator = require('../util/validator');

module.exports = function (server) {
	server.post('/createTh_r', validator.typeIsJson, api.saveTh_r);
	server.get('/th_r_list', api.getList);
	server.post('/updateTh_r', validator.typeIsJson, api.updateTh_r);
	server.post('/delTh_r_list', validator.typeIsJson, api.delTh_r_list);
};