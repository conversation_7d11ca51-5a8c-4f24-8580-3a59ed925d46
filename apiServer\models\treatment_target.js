/**
 * 待遇协议表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');

const TmtTargetSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {	// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		settling_in_allowance: {	// 安家费
			type: String,
			trim: true
		},
		scientific_research_funds: {	// 科研经费
			type: String,
			trim: true
		},
		lab_construction_fee: {	// 实验室建设费
			type: String,
			trim: true
		},
		housing: {	// 住房
			type: String,
			trim: true
		},
		housing_subsidies: {	// 购房补贴
			type: String,
			trim: true
		},
		makeshift_house: {	// 过渡房
			type: String,
			trim: true
		},
		scientific_research_house: {	// 科研用房
			type: String,
			trim: true
		},
		title_tp: {	// 职称特评
			type: String,
			trim: true
		},
		spouse_work: {	// 配偶工作
			type: String,
			trim: true
		},
		remark: {	// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

TmtTargetSchema.plugin(timestamps);

const TreatmentTarget = mongoose.model('treatment_target', TmtTargetSchema, 'treatment_targets');

class TreatmentTargetUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof TreatmentTargetUtil
	 */
	assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.settling_in_allowance !== 'undefined' && (target.settling_in_allowance = source.settling_in_allowance);		// 安家费
		typeof source.scientific_research_funds !== 'undefined' && (target.scientific_research_funds = source.scientific_research_funds);		// 科研经费
		typeof source.lab_construction_fee !== 'undefined' && (target.lab_construction_fee = source.lab_construction_fee);		// 实验室建设费
		typeof source.housing !== 'undefined' && (target.housing = source.housing);		// 住房
		typeof source.housing_subsidies !== 'undefined' && (target.housing_subsidies = source.housing_subsidies);		// 购房补贴
		typeof source.makeshift_house !== 'undefined' && (target.makeshift_house = source.makeshift_house);		// 过渡房
		typeof source.scientific_research_house !== 'undefined' && (target.scientific_research_house = source.scientific_research_house);		// 科研用房
		typeof source.title_tp !== 'undefined' && (target.title_tp = source.title_tp);		// 职称特评
		typeof source.spouse_work !== 'undefined' && (target.spouse_work = source.spouse_work);		// 配偶工作
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const treatmentTargetUtil = new TreatmentTargetUtil();

module.exports = { TreatmentTarget, treatmentTargetUtil };    