/**
 * 项目成果表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');

const { userUtil } = require('./user');

const ProjectResultSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		ssxb: {// 所属系部
			type: String,
			trim: true
		},
		fzr: {// 负责人
			type: String,
			trim: true
		},
		xmlx: {// 项目类型
			type: String,
			trim: true
		},
		lxnd: {// 立项年度
			type: String,
			trim: true
		},
		xmxh: {// 项目序号
			type: String,
			trim: true
		},
		xmmc: {// 项目名称
			type: String,
			trim: true
		},
		xmbh: {// 项目编号
			type: String,
			trim: true
		},
		xmzjly: {// 项目直接来源
			type: String,
			trim: true
		},
		xmjb: {// 项目级别
			type: String,
			trim: true
		},
		lxpzje: {// 立项批准金额
			type: String,
			trim: true
		},
		remark: {// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

ProjectResultSchema.plugin(timestamps);

const ProjectResult = mongoose.model('project_result', ProjectResultSchema, 'project_results');

class ProjectResultUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ProjectResultUtil
	 */
	async assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);	
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.ssxb !== 'undefined' && (target.ssxb = source.ssxb);		// 所属系部
		typeof source.fzr !== 'undefined' && (target.fzr = source.fzr);		// 负责人
		typeof source.xmlx !== 'undefined' && (target.xmlx = source.xmlx);		// 项目类型
		typeof source.lxnd !== 'undefined' && (target.lxnd = source.lxnd);		// 立项年度
		typeof source.xmxh !== 'undefined' && (target.xmxh = source.xmxh);		// 项目序号
		typeof source.xmmc !== 'undefined' && (target.xmmc = source.xmmc);		// 项目名称
		typeof source.xmbh !== 'undefined' && (target.xmbh = source.xmbh);		// 项目编号
		typeof source.xmzjly !== 'undefined' && (target.xmzjly = source.xmzjly);		// 项目直接来源
		typeof source.xmjb !== 'undefined' && (target.xmjb = source.xmjb);		// 项目级别
		typeof source.lxpzje !== 'undefined' && (target.lxpzje = source.lxpzje);		// 立项批准金额
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		if (typeof source.user_id !== 'undefined') { // 关联查询信息
			if (typeof source.user_id.num != 'undefined') {
				target.user_id = await userUtil.assign(source.user_id);
			} else {
				target.user_id = source.user_id;
			}
		}

		return target;
	}
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ProjectResultUtil
	 */
	assign_export(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);	
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.ssxb !== 'undefined' && (target.ssxb = source.ssxb);		// 所属系部
		typeof source.fzr !== 'undefined' && (target.fzr = source.fzr);		// 负责人
		typeof source.xmlx !== 'undefined' && (target.xmlx = source.xmlx);		// 项目类型
		typeof source.lxnd !== 'undefined' && (target.lxnd = source.lxnd);		// 立项年度
		typeof source.xmxh !== 'undefined' && (target.xmxh = source.xmxh);		// 项目序号
		typeof source.xmmc !== 'undefined' && (target.xmmc = source.xmmc);		// 项目名称
		typeof source.xmbh !== 'undefined' && (target.xmbh = source.xmbh);		// 项目编号
		typeof source.xmzjly !== 'undefined' && (target.xmzjly = source.xmzjly);		// 项目直接来源
		typeof source.xmjb !== 'undefined' && (target.xmjb = source.xmjb);		// 项目级别
		typeof source.lxpzje !== 'undefined' && (target.lxpzje = source.lxpzje);		// 立项批准金额
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const projectResultUtil = new ProjectResultUtil();

module.exports = { ProjectResult, projectResultUtil };    