/**
 * 其他目标表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');

const OtherTargetSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {	// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		sbrcch: {	// 申报人才称号
			type: String,
			trim: true
		},
		tjrc: {	// 推荐人才
			type: String,
			trim: true
		},
		hjqk: {	// 获奖情况
			type: String,
			trim: true
		},
		remark: {	// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

OtherTargetSchema.plugin(timestamps);

const OtherTarget = mongoose.model('other_target', OtherTargetSchema, 'other_targets');

class OtherTargetUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof OtherTargetUtil
	 */
	assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.sbrcch !== 'undefined' && (target.sbrcch = source.sbrcch);		// 申报人才称号
		typeof source.tjrc !== 'undefined' && (target.tjrc = source.tjrc);		// 推荐人才
		typeof source.hjqk !== 'undefined' && (target.hjqk = source.hjqk);		// 获奖
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const otherTargetUtil = new OtherTargetUtil();

module.exports = { OtherTarget, otherTargetUtil };    