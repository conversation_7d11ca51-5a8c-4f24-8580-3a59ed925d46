/**
 * 论文成果表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');
const util = require('../util/util');
const { thesisType } = require('../dictionary');

const { userUtil } = require('./user');

const ThesisResultSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {// 人员代码
			unique: true,
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		zzbm: {// 作者部门
			type: String,
			trim: true
		},
		zzbx: {// 作者排序
			type: String,
			trim: true
		},
		zzxnpx: {// 作者校内排序
			type: String,
			trim: true
		},
		tj_year: {// 提交年度
			type: String,
			trim: true
		},
		lx: {// 类型
			type: String,
			trim: true
		},
		lwmc: {// 论文名称
			type: String,
			trim: true
		},
		fbqk: {// 发表期刊
			type: String,
			trim: true
		},
		zz: {// 作者
			type: String,
			trim: true
		},
		fbsj: {// 发表时间
			type: Date
		},
		j: {// 卷
			type: String,
			trim: true
		},
		slqk: {// 收录情况
			type: String,
			trim: true
		},
		q: {// 期
			type: String,
			trim: true
		},
		gwhxqk: {// 国外核心期刊
			type: String,
			trim: true
		},
		gwybqk: {// 国外一般期刊
			type: String,
			trim: true
		},
		txzz: {// 通讯作者
			type: String,
			trim: true
		},
		gnzyqk: {// 国内重要期刊
			type: String,
			trim: true
		},
		dyzz: {// 第一作者
			type: String,
			trim: true
		},
		dyzz_zjxs: {// 第一作者是否为在籍学生
			type: Boolean
		},
		zyqkzbdw: {// 重要期刊主办单位
			type: String,
			trim: true
		},
		xsxh: {// 学生学号
			type: String,
			trim: true
		},
		xsxm: {// 学生姓名
			type: String,
			trim: true
		},
		gwqtkw: {// 国外其他刊物
			type: String,
			trim: true
		},
		lwlx: {// 论文类型
			type: String,
			trim: true
		},
		cssci_kyqk: {// CSSCI刊源期刊
			type: String,
			trim: true
		},
		gnzwhxqk: {// 国内中文核心期刊
			type: String,
			trim: true
		},
		cscd_kyqk: {// CSCD刊源期刊
			type: String,
			trim: true
		},
		issn: {// ISSN
			type: String,
			trim: true
		},
		zeng_zheng: {// 增刊还是正刊
			type: String,
			trim: true
		},
		yy: {// 语言
			type: String,
			trim: true
		},
		yxyz: {// 影响因子
			type: String,
			trim: true
		},
		lwlx_custom: {// 自定义论文类型
			"type": Number,
			"enum": [thesisType.sci, thesisType.sci1, thesisType.sci2],
		},
		remark: {// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

ThesisResultSchema.plugin(timestamps);

const ThesisResult = mongoose.model('thesis_result', ThesisResultSchema, 'thesis_results');

class ThesisResultUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ThesisResultUtil
	 */
	async assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.zzbm !== 'undefined' && (target.zzbm = source.zzbm);		// 作者部门
		typeof source.zzbx !== 'undefined' && (target.zzbx = source.zzbx);		// 作者排序
		typeof source.zzxnpx !== 'undefined' && (target.zzxnpx = source.zzxnpx);		// 作者校内排序
		typeof source.tj_year !== 'undefined' && (target.tj_year = source.tj_year);		// 提交年度
		typeof source.lx !== 'undefined' && (target.lx = source.lx);		// 类型
		typeof source.lwmc !== 'undefined' && (target.lwmc = source.lwmc);		// 论文名称
		typeof source.fbqk !== 'undefined' && (target.fbqk = source.fbqk);		// 发表期刊
		typeof source.zz !== 'undefined' && (target.zz = source.zz);		// 作者
		target.fbsj = util.getTime(source.fbsj);		// 发表时间
		typeof source.j !== 'undefined' && (target.j = source.j);		// 卷
		typeof source.slqk !== 'undefined' && (target.slqk = source.slqk);		// 收录情况
		typeof source.q !== 'undefined' && (target.q = source.q);		// 期		
		typeof source.gwhxqk !== 'undefined' && (target.gwhxqk = source.gwhxqk);		// 国外核心期刊
		typeof source.gwybqk !== 'undefined' && (target.gwybqk = source.gwybqk);		// 国外一般期刊
		typeof source.txzz !== 'undefined' && (target.txzz = source.txzz);		// 通讯作者
		typeof source.gnzyqk !== 'undefined' && (target.gnzyqk = source.gnzyqk);		// 国内重要期刊
		typeof source.dyzz !== 'undefined' && (target.dyzz = source.dyzz);		// 第一作者
		if (typeof source.dyzz_zjxs === 'boolean') {	// 第一作者是否为在籍学生
			target.dyzz_zjxs = source.dyzz_zjxs;
		} else if (typeof source.dyzz_zjxs !== 'undefined' && source.dyzz_zjxs !== '') {
			target.dyzz_zjxs = !!source.dyzz_zjxs;
		}
		typeof source.zyqkzbdw !== 'undefined' && (target.zyqkzbdw = source.zyqkzbdw);		// 重要期刊主办单位
		typeof source.xsxh !== 'undefined' && (target.xsxh = source.xsxh);		// 学生学号
		typeof source.xsxm !== 'undefined' && (target.xsxm = source.xsxm);		// 学生姓名
		typeof source.gwqtkw !== 'undefined' && (target.gwqtkw = source.gwqtkw);		// 国外其他刊物
		typeof source.lwlx !== 'undefined' && (target.lwlx = source.lwlx);		// 论文类型
		typeof source.cssci_kyqk !== 'undefined' && (target.cssci_kyqk = source.cssci_kyqk);		// CSSCI刊源期刊
		typeof source.gnzwhxqk !== 'undefined' && (target.gnzwhxqk = source.gnzwhxqk);		// 国内中文核心期刊
		typeof source.cscd_kyqk !== 'undefined' && (target.cscd_kyqk = source.cscd_kyqk);		// CSCD刊源期刊
		typeof source.issn !== 'undefined' && (target.issn = source.issn);		// ISSN
		typeof source.zeng_zheng !== 'undefined' && (target.zeng_zheng = source.zeng_zheng);		// 增刊还是正刊
		typeof source.yy !== 'undefined' && (target.yy = source.yy);		// 语言
		typeof source.yxyz !== 'undefined' && (target.yxyz = source.yxyz);		// 影响因子
		typeof source.lwlx_custom !== 'undefined' && [thesisType.sci, thesisType.sci1, thesisType.sci2].includes(parseInt(source.lwlx_custom)) && (target.lwlx_custom = parseInt(source.lwlx_custom));		// 自定义论文类型
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		if (typeof source.user_id !== 'undefined') { // 关联查询信息
			if (typeof source.user_id.num != 'undefined') {
				target.user_id = await userUtil.assign(source.user_id);
			} else {
				target.user_id = source.user_id;
			}
		}

		return target;
	}
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof ThesisResultUtil
	 */
	assign_export(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.zzbm !== 'undefined' && (target.zzbm = source.zzbm);		// 作者部门
		typeof source.zzbx !== 'undefined' && (target.zzbx = source.zzbx);		// 作者排序
		typeof source.zzxnpx !== 'undefined' && (target.zzxnpx = source.zzxnpx);		// 作者校内排序
		typeof source.tj_year !== 'undefined' && (target.tj_year = source.tj_year);		// 提交年度
		typeof source.lx !== 'undefined' && (target.lx = source.lx);		// 类型
		typeof source.lwmc !== 'undefined' && (target.lwmc = source.lwmc);		// 论文名称
		typeof source.fbqk !== 'undefined' && (target.fbqk = source.fbqk);		// 发表期刊
		typeof source.zz !== 'undefined' && (target.zz = source.zz);		// 作者
		target.fbsj = util.getTime(source.fbsj);		// 发表时间
		typeof source.j !== 'undefined' && (target.j = source.j);		// 卷
		typeof source.slqk !== 'undefined' && (target.slqk = source.slqk);		// 收录情况
		typeof source.q !== 'undefined' && (target.q = source.q);		// 期
		typeof source.gwhxqk !== 'undefined' && (target.gwhxqk = source.gwhxqk);		// 国外核心期刊
		typeof source.gwybqk !== 'undefined' && (target.gwybqk = source.gwybqk);		// 国外一般期刊
		typeof source.txzz !== 'undefined' && (target.txzz = source.txzz);		// 通讯作者
		typeof source.gnzyqk !== 'undefined' && (target.gnzyqk = source.gnzyqk);		// 国内重要期刊
		typeof source.dyzz !== 'undefined' && (target.dyzz = source.dyzz);		// 第一作者
		if (typeof source.dyzz_zjxs === 'boolean') {	// 第一作者是否为在籍学生
			target.dyzz_zjxs = source.dyzz_zjxs;
		} else if (typeof source.dyzz_zjxs !== 'undefined' && source.dyzz_zjxs !== '') {
			target.dyzz_zjxs = !!source.dyzz_zjxs;
		}
		typeof source.zyqkzbdw !== 'undefined' && (target.zyqkzbdw = source.zyqkzbdw);		// 重要期刊主办单位
		typeof source.xsxh !== 'undefined' && (target.xsxh = source.xsxh);		// 学生学号
		typeof source.xsxm !== 'undefined' && (target.xsxm = source.xsxm);		// 学生姓名
		typeof source.gwqtkw !== 'undefined' && (target.gwqtkw = source.gwqtkw);		// 国外其他刊物
		typeof source.lwlx !== 'undefined' && (target.lwlx = source.lwlx);		// 论文类型
		typeof source.cssci_kyqk !== 'undefined' && (target.cssci_kyqk = source.cssci_kyqk);		// CSSCI刊源期刊
		typeof source.gnzwhxqk !== 'undefined' && (target.gnzwhxqk = source.gnzwhxqk);		// 国内中文核心期刊
		typeof source.cscd_kyqk !== 'undefined' && (target.cscd_kyqk = source.cscd_kyqk);		// CSCD刊源期刊
		typeof source.issn !== 'undefined' && (target.issn = source.issn);		// ISSN
		typeof source.zeng_zheng !== 'undefined' && (target.zeng_zheng = source.zeng_zheng);		// 增刊还是正刊
		typeof source.yy !== 'undefined' && (target.yy = source.yy);		// 语言
		typeof source.yxyz !== 'undefined' && (target.yxyz = source.yxyz);		// 影响因子
		typeof source.lwlx_custom !== 'undefined' && [thesisType.sci, thesisType.sci1, thesisType.sci2].includes(parseInt(source.lwlx_custom)) && (target.lwlx_custom = parseInt(source.lwlx_custom));		// 自定义论文类型
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		return target;
	}
}

const thesisResultUtil = new ThesisResultUtil();

module.exports = { ThesisResult, thesisResultUtil };    