/*
 * @Author: zpl
 * @LastEditors: zpl
 * @Description: 初始化数据库
 * @Date: 2019-04-30 10:34:56
 * @LastEditTime: 2019-06-19 17:50:06
 */

/**
 * Module Dependencies
 */
const dictionary = require('../dictionary');

/**
 * Model Schema
 */
const { User } = require('../models/user');

module.exports = async function () {
  User.findOne({num: 'admin'}).then(result => {
    if(!result){
      let user = new User({
        "level": dictionary.userLevel.administrator,
        "num": "admin",
        "name": "admin",
        "sex": "男"
      });
      return user.save();
    } else {
      return Promise.resolve({message: '数据库无需初始化。'});
    }
  }).then(msg => {
    console.log(msg.message || '数据库初始化成功。');
  }).catch(e => {
    console.error('数据库初始化失败！！！');
  });
};