import dva from 'dva';
import { Component } from 'react';
import createLoading from 'dva-loading';
import history from '@tmp/history';

let app = null;

export function _onCreate() {
  const plugins = require('umi/_runtimePlugin');
  const runtimeDva = plugins.mergeConfig('dva');
  app = dva({
    history,
    
    ...(runtimeDva.config || {}),
    ...(window.g_useSSR ? { initialState: window.g_initialData } : {}),
  });
  
  app.use(createLoading());
  (runtimeDva.plugins || []).forEach(plugin => {
    app.use(plugin);
  });
  
  app.model({ namespace: 'global', ...(require('E:/codes/gcc-xkd/myClient/src/models/global.js').default) });
app.model({ namespace: 'list', ...(require('E:/codes/gcc-xkd/myClient/src/models/list.js').default) });
app.model({ namespace: 'login', ...(require('E:/codes/gcc-xkd/myClient/src/models/login.js').default) });
app.model({ namespace: 'menu', ...(require('E:/codes/gcc-xkd/myClient/src/models/menu.js').default) });
app.model({ namespace: 'other_r', ...(require('E:/codes/gcc-xkd/myClient/src/models/other_r.js').default) });
app.model({ namespace: 'personnel', ...(require('E:/codes/gcc-xkd/myClient/src/models/personnel.js').default) });
app.model({ namespace: 'project_r', ...(require('E:/codes/gcc-xkd/myClient/src/models/project_r.js').default) });
app.model({ namespace: 'project', ...(require('E:/codes/gcc-xkd/myClient/src/models/project.js').default) });
app.model({ namespace: 'report', ...(require('E:/codes/gcc-xkd/myClient/src/models/report.js').default) });
app.model({ namespace: 'setting', ...(require('E:/codes/gcc-xkd/myClient/src/models/setting.js').default) });
app.model({ namespace: 'supportingDoc', ...(require('E:/codes/gcc-xkd/myClient/src/models/supportingDoc.js').default) });
app.model({ namespace: 'thesis_r', ...(require('E:/codes/gcc-xkd/myClient/src/models/thesis_r.js').default) });
app.model({ namespace: 'treatment_r', ...(require('E:/codes/gcc-xkd/myClient/src/models/treatment_r.js').default) });
app.model({ namespace: 'user', ...(require('E:/codes/gcc-xkd/myClient/src/models/user.js').default) });
  return app;
}

export function getApp() {
  return app;
}

export class _DvaContainer extends Component {
  render() {
    const app = getApp();
    app.router(() => this.props.children);
    return app.start()();
  }
}
