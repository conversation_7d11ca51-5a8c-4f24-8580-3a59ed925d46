/**
 * Module Dependencies
 */
const config = require("./config");
const restify = require("restify");
const corsMiddleware = require("restify-cors-middleware");
const mongoose = require("mongoose");

/**
 * Initialize Server
 */
const server = restify.createServer({
  name: config.name,
  version: config.version,
});

const cors = corsMiddleware({
  origins: ["http://localhost:8000", "http://*************:8000"],
  credentials: true,
});

/**
 * Middleware
 */
server.pre(cors.preflight);
server.use(cors.actual);
server.use(restify.plugins.jsonBodyParser({ mapParams: true }));
server.use(restify.plugins.acceptParser(server.acceptable));
server.use(restify.plugins.queryParser({ mapParams: true }));
server.use(restify.plugins.fullResponse());
// server.use((req, res, next) => {
// 所有请求入口，可以用来测试请求到达情况
// 	console.log('---------')
// 	next();
// })

/**
 * Start Server, Connect to DB & Require Routes
 */
server.listen(config.port, () => {
  // establish connection to mongodb
  mongoose.Promise = global.Promise;
  mongoose.set('strictQuery', false);
  mongoose.connect(config.db.uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  const db = mongoose.connection;

  db.on("error", err => {
    console.error(err);
    process.exit(1);
  });

  db.once("open", () => {
    require("./util/init_db")(); // 初始化数据库
    require("./util/init_directory")(); // 初始化文件夹
    require("./routes/api")(server); // 业务接口
    require("./routes/user")(server); // 用户
    require("./routes/treatment_target")(server); // 待遇协议
    require("./routes/thesis_target")(server); // 论文目标
    require("./routes/project_target")(server); // 项目目标
    require("./routes/other_target")(server); // 其他目标
    require("./routes/treatment_result")(server); // 待遇兑现
    require("./routes/thesis_result")(server); // 论文成果
    require("./routes/project_result")(server); // 项目成果
    require("./routes/other_result")(server); // 其他成果
    require("./routes/supporting_document")(server); // 支撑材料
    console.log(`Server is listening on port ${config.port}`);
  });
});
