/**
 * 待遇兑现表
 */
const mongoose = require('mongoose');
const timestamps = require('mongoose-timestamp');
const util = require('../util/util');

const { userUtil } = require('./user');

const TmtResultSchema = new mongoose.Schema(
	{
		user_id: {
			type: mongoose.Schema.Types.ObjectId,
			ref: 'user'
		},
		num: {// 人员代码
			type: String,
			required: true,
			trim: true,
			minlength: 1
		},
		settling_in_allowance: {// 安家费
			type: String,
			trim: true
		},
		scientific_research_funds: {// 科研经费
			type: String,
			trim: true
		},
		lab_construction_fee: {// 实验室建设费
			type: String,
			trim: true
		},
		housing: {// 住房
			type: String,
			trim: true
		},
		housing_date: {// 住房分配时间
			type: Date
		},
		housing_subsidies: {// 购房补贴
			type: Array,
			trim: true
		},
		makeshift_house: {// 过渡住房
			type: String,
			trim: true
		},
		makeshift_house_date: {// 过渡住房分配时间
			type: Date
		},
		scientific_research_house: {// 科研用房
			type: String,
			trim: true
		},
		srh_date: {// 科研用房分配时间
			type: Date
		},
		title_tp: {// 职称特评
			type: String,
			trim: true
		},
		title_tp_date: {// 职称特评分配时间
			type: Date
		},
		spouse_work: {// 配偶工作
			type: String,
			trim: true
		},
		spouse_work_date: {// 配偶工作安排时间
			type: Date
		},
		remark: {// 备注
			type: String,
			trim: true
		}
	},
	{ autoIndex: false, minimize: false },
);

TmtResultSchema.plugin(timestamps);

const TreatmentResult = mongoose.model('treatment_result', TmtResultSchema, 'treatment_results');

class TreatmentResultUtil {
	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @returns
	 * @memberof TreatmentResultUtil
	 */
	async assign(source) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		source._id && typeof source._id !== 'undefined' && (target._id = source._id);	
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		typeof source.settling_in_allowance !== 'undefined' && (target.settling_in_allowance = source.settling_in_allowance);		// 安家费
		typeof source.scientific_research_funds !== 'undefined' && (target.scientific_research_funds = source.scientific_research_funds);		// 科研经费
		typeof source.lab_construction_fee !== 'undefined' && (target.lab_construction_fee = source.lab_construction_fee);		// 实验室建设费
		typeof source.housing !== 'undefined' && (target.housing = source.housing);		// 住房
		target.housing_date = util.getTime(source.housing_date);	// 住房分配时间
		typeof source.housing_subsidies !== 'undefined' && (target.housing_subsidies = source.housing_subsidies);		// 购房补贴
		typeof source.makeshift_house !== 'undefined' && (target.makeshift_house = source.makeshift_house);		// 过渡房
		target.makeshift_house_date = util.getTime(source.makeshift_house_date);		// 过渡房分配时间
		typeof source.scientific_research_house !== 'undefined' && (target.scientific_research_house = source.scientific_research_house);		// 科研用房
		target.srh_date = util.getTime(source.srh_date);		// 科研用房分配时间
		typeof source.title_tp !== 'undefined' && (target.title_tp = source.title_tp);		// 职称特评
		target.title_tp_date = util.getTime(source.title_tp_date);		// 职称特评时间
		typeof source.spouse_work !== 'undefined' && (target.spouse_work = source.spouse_work);		// 配偶工作
		target.spouse_work_date = util.getTime(source.spouse_work_date);		// 配偶工作安排时间
		typeof source.remark !== 'undefined' && (target.remark = source.remark);		// 备注

		if (typeof source.user_id !== 'undefined') { // 关联查询信息
			if(typeof source.user_id.num != 'undefined'){
				target.user_id = await userUtil.assign(source.user_id);
			} else {
				target.user_id = source.user_id;
			}
		}	

		return target;
	}

	/**
	 * 从原始数据中提取有效信息
	 *
	 * @param {JSON} source
	 * @param {Array} years
	 * @param {String} tableDir 时间排列方向，row 横向，col 纵向
	 * @returns
	 * @memberof TreatmentResultUtil
	 */
	assign_export(source, years, tableDir) {
		let target = {};
		if (!source || typeof source === 'undefined') {
			return target;
		}
		typeof source.num !== 'undefined' && (target.num = source.num);		// 人员代码
		util.convertListToYearValue(target, 'settling_in_allowance__r', source.settling_in_allowance, years, tableDir);		// 安家费
		util.convertListToYearValue(target, 'scientific_research_funds__r', source.scientific_research_funds, years, tableDir);		// 科研经费
		util.convertListToYearValue(target, 'lab_construction_fee__r', source.lab_construction_fee, years, tableDir);		// 实验室建设费
		typeof source.housing !== 'undefined' && (target.housing__r = source.housing);		// 住房
		const housing_date__r = util.getTime(source.housing_date);
		target.housing_date__r = housing_date__r ? `${new Date(housing_date__r).getFullYear()}年${new Date(housing_date__r).getMonth() + 1}月${new Date(housing_date__r).getDate()}日` : ``;	// 住房分配时间
		util.convertListToYearValue(target, 'housing_subsidies__r', source.housing_subsidies, years, tableDir);		// 购房补贴
		// typeof source.housing_subsidies !== 'undefined' && (target.housing_subsidies = source.housing_subsidies);
		typeof source.makeshift_house !== 'undefined' && (target.makeshift_house__r = source.makeshift_house);		// 过渡房
		const makeshift_house_date__r = util.getTime(source.makeshift_house_date);
		target.makeshift_house_date__r = makeshift_house_date__r ? `${new Date(makeshift_house_date__r).getFullYear()}年${new Date(makeshift_house_date__r).getMonth() + 1}月${new Date(makeshift_house_date__r).getDate()}日` : ``;		// 过渡房分配时间
		typeof source.scientific_research_house !== 'undefined' && (target.scientific_research_house__r = source.scientific_research_house);		// 科研用房
		const srh_date__r = util.getTime(source.srh_date);
		target.srh_date__r = srh_date__r ? `${new Date(srh_date__r).getFullYear()}年${new Date(srh_date__r).getMonth() + 1}月${new Date(srh_date__r).getDate()}日` : ``;		// 科研用房分配时间
		typeof source.title_tp !== 'undefined' && (target.title_tp__r = source.title_tp);		// 职称特评
		const title_tp_date__r = util.getTime(source.title_tp_date);
		target.title_tp_date__r = title_tp_date__r ? `${new Date(title_tp_date__r).getFullYear()}年${new Date(title_tp_date__r).getMonth() + 1}月${new Date(title_tp_date__r).getDate()}日` : ``;		// 职称特评时间
		typeof source.spouse_work !== 'undefined' && (target.spouse_work__r = source.spouse_work);		// 配偶工作
		const spouse_work_date__r = util.getTime(source.spouse_work_date);
		target.spouse_work_date__r = spouse_work_date__r ? `${new Date(spouse_work_date__r).getFullYear()}年${new Date(spouse_work_date__r).getMonth() + 1}月${new Date(spouse_work_date__r).getDate()}日` : ``;		// 配偶工作安排时间
		typeof source.remark__r !== 'undefined' && (target.remark__r = source.remark);		// 备注

		return target;
	}
}

const treatmentResultUtil = new TreatmentResultUtil();

module.exports = { TreatmentResult, treatmentResultUtil };    