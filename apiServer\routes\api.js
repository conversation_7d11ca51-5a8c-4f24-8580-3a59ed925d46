/*
 * @Author: zpl
 * @LastEditors: zpl
 * @Description: 相关业务接口
 * @Date: 2019-04-30 10:34:56
 * @LastEditTime: 2019-06-19 21:32:44
 */

/**
* Module Dependencies
*/
const restify = require('restify');
const path = require('path');
const apis = require('../api/api');
const validator = require('../util/validator');

/**
 * Upload Dependencies
 */
const JFUM = require('jfum');
const jfum = new JFUM({
  minFileSize: 1024,                      // 1 kB
  maxFileSize: 52428800,                     // 50 mB
  acceptFileTypes: /\.(gif|jpe?g|png|pdf)$/i    // gif, jpg, jpeg, png, pdf
});

const dictionary = require('../dictionary');
const uploadPath = dictionary.upload.savePath;
const exportPath = dictionary.export.savePath;

module.exports = function (server) {
  // 登录验证
  server.post('/doLogin', validator.typeIsJson, apis.doLogin);

  // 查询个人基本情况报表
  server.post('/queryBaseInfo', apis.queryBaseInfo);

  // 导出个人基本情况报表
  server.post('/exportBaseInfo', validator.typeIsJson, apis.exportBaseInfo);

  // 导出汇总表
  server.post('/exportToExcel', validator.typeIsJson, apis.exportToExcel);

  // 下载导出的文件
  server.get('/downloadExport/:fileName', restify.plugins.serveStatic({
    directory: exportPath,
    appendRequestPath: false
  }));

  // 上传文件
  server.opts('/upload', jfum.optionsHandler.bind(jfum));
  server.post('/upload', jfum.postHandler.bind(jfum), apis.upload);

  // 下载文件
  server.get('/download/:fileName', restify.plugins.serveStatic({
    directory: uploadPath,
    appendRequestPath: false,
    charSet: 'utf-8'
  }));
};