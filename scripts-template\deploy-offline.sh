#!/bin/bash

# GCC-XKD Offline Deployment Script for Linux/macOS
# 专用于离线环境部署，不进行任何构建操作

CONFIG_DIR="config"

echo -e "\033[32mGCC-XKD Offline Deployment Tool\033[0m"
echo -e "\033[32m===============================\033[0m"

# Check Docker environment
check_docker_environment() {
    echo -e "\033[33mChecking Docker environment...\033[0m"

    if ! command -v docker &> /dev/null; then
        echo -e "\033[31mDocker not installed\033[0m"
        return 1
    fi
    echo -e "\033[32mDocker installed\033[0m"

    if ! docker info &> /dev/null; then
        echo -e "\033[31mDocker service not started\033[0m"
        return 1
    fi
    echo -e "\033[32mDocker service is running\033[0m"

    if ! command -v docker-compose &> /dev/null; then
        echo -e "\033[31mDocker Compose not installed\033[0m"
        return 1
    fi
    echo -e "\033[32mDocker Compose installed\033[0m"

    return 0
}

# Check port availability
check_port_availability() {
    echo -e "\033[33mChecking port availability...\033[0m"

    ports=(80 3000 27018)
    all_available=true

    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            echo -e "\033[31mPort $port is in use\033[0m"
            all_available=false
        else
            echo -e "\033[32mPort $port is available\033[0m"
        fi
    done

    if [ "$all_available" = true ]; then
        return 0
    else
        return 1
    fi
}

# Import Docker images from images directory
import_docker_images() {
    echo -e "\033[33mImporting Docker images...\033[0m"

    images_dir="images"
    if [ ! -d "$images_dir" ]; then
        echo -e "\033[31mImages directory not found: $images_dir\033[0m"
        return 1
    fi

    image_files=($(find "$images_dir" -name "*.tar" -type f))
    if [ ${#image_files[@]} -eq 0 ]; then
        echo -e "\033[31mNo image files found in $images_dir\033[0m"
        return 1
    fi

    imported=0
    for image_file in "${image_files[@]}"; do
        filename=$(basename "$image_file")
        echo -e "\033[36mImporting: $filename\033[0m"
        
        if docker load -i "$image_file"; then
            ((imported++))
            echo -e "\033[32mImported: $filename\033[0m"
        else
            echo -e "\033[31m✗ Failed to import: $filename\033[0m"
        fi
    done

    echo -e "\033[32mImage import completed: $imported/${#image_files[@]} files imported\033[0m"
    [ $imported -gt 0 ]
}

# Copy configuration and start services
start_offline_services() {
    echo -e "\033[33mStarting services in offline mode...\033[0m"

    # Copy docker-compose.yml from config directory
    compose_file="$CONFIG_DIR/docker-compose.yml"
    if [ ! -f "$compose_file" ]; then
        echo -e "\033[31mdocker-compose.yml not found in $CONFIG_DIR\033[0m"
        return 1
    fi

    cp "$compose_file" "docker-compose.yml"
    echo -e "\033[32mConfiguration file copied\033[0m"

    # Start services without building
    echo -e "\033[36mStarting Docker services...\033[0m"
    if docker-compose up -d --no-build; then
        echo -e "\033[32mServices started successfully\033[0m"
        return 0
    else
        echo -e "\033[31m✗ Failed to start services\033[0m"
        return 1
    fi
}

# Wait for services to be ready
wait_services_ready() {
    echo -e "\033[33mWaiting for services to be ready...\033[0m"

    max_wait=60
    waited=0

    while [ $waited -lt $max_wait ]; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health | grep -q "200"; then
            echo -e "\033[32mBackend service is ready\033[0m"
            break
        fi

        sleep 2
        waited=$((waited + 2))
        echo -e "\033[36mWaiting... ($waited/${max_wait}s)\033[0m"
    done

    if [ $waited -ge $max_wait ]; then
        echo -e "\033[33m! Service startup timeout, but may still be initializing\033[0m"
    fi
}

# Show access information
show_access_info() {
    echo ""
    echo -e "\033[32m========================================\033[0m"
    echo -e "\033[32mOffline deployment completed!\033[0m"
    echo -e "\033[32m========================================\033[0m"
    echo ""
    echo -e "\033[36mAccess URLs:\033[0m"
    echo -e "\033[37m- Frontend: http://localhost\033[0m"
    echo -e "\033[37m- Backend API: http://localhost:3000\033[0m"
    echo -e "\033[37m- MongoDB: localhost:27018\033[0m"
    echo ""
    echo -e "\033[36mManagement commands:\033[0m"
    echo -e "\033[37m- Check status: docker-compose ps\033[0m"
    echo -e "\033[37m- View logs: docker-compose logs -f\033[0m"
    echo -e "\033[37m- Stop services: docker-compose down\033[0m"
    echo -e "\033[37m- Restart services: docker-compose restart\033[0m"
    echo ""
    echo -e "\033[33mMongoDB connection: ./connect-mongodb.sh\033[0m"
    echo ""
}

# Main execution
echo ""

# 1. Check Docker environment
if ! check_docker_environment; then
    echo ""
    echo -e "\033[31mPlease install and start Docker environment first\033[0m"
    read -p "Press any key to exit..."
    exit 1
fi

echo ""

# 2. Check ports
if ! check_port_availability; then
    echo ""
    echo -e "\033[31mPlease free up occupied ports and try again\033[0m"
    read -p "Press any key to exit..."
    exit 1
fi

echo ""

# 3. Import Docker images
if ! import_docker_images; then
    echo ""
    echo -e "\033[31mFailed to import Docker images\033[0m"
    read -p "Press any key to exit..."
    exit 1
fi

echo ""

# 4. Start services (no build)
if ! start_offline_services; then
    echo ""
    echo -e "\033[31mFailed to start services\033[0m"
    read -p "Press any key to exit..."
    exit 1
fi

echo ""

# 5. Wait for services to be ready
wait_services_ready

# 6. Show access information
show_access_info

echo ""
read -p "Press any key to exit..."
