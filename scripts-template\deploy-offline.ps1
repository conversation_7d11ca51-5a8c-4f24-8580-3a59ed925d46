# GCC-XKD Offline Deployment Script
# 专用于离线环境部署，不进行任何构建操作

param(
    [string]$ConfigDir = "config"
)

Write-Host "GCC-XKD Offline Deployment Tool" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Check Docker environment
function Test-DockerEnvironment {
    Write-Host "Checking Docker environment..." -ForegroundColor Yellow

    try {
        & docker --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker not installed" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker installed" -ForegroundColor Green
    } catch {
        Write-Host "Docker not installed" -ForegroundColor Red
        return $false
    }

    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker service not started" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker service is running" -ForegroundColor Green
    } catch {
        Write-Host "Docker service not started" -ForegroundColor Red
        return $false
    }

    try {
        & docker-compose --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker Compose not installed" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker Compose installed" -ForegroundColor Green
    } catch {
        Write-Host "Docker Compose not installed" -ForegroundColor Red
        return $false
    }

    return $true
}

# Check port availability
function Test-PortAvailability {
    Write-Host "Checking port availability..." -ForegroundColor Yellow

    $ports = @(80, 3000, 27018)
    $allAvailable = $true

    foreach ($port in $ports) {
        try {
            $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
            if ($connection.TcpTestSucceeded) {
                Write-Host ("Port " + $port + " is in use") -ForegroundColor Red
                $allAvailable = $false
            } else {
                Write-Host ("Port " + $port + " is available") -ForegroundColor Green
            }
        } catch {
            Write-Host ("Port " + $port + " is available") -ForegroundColor Green
        }
    }

    return $allAvailable
}

# Import Docker images from images directory
function Import-DockerImages {
    Write-Host "Importing Docker images..." -ForegroundColor Yellow

    $imagesDir = "images"
    if (-not (Test-Path $imagesDir)) {
        Write-Host "Images directory not found: $imagesDir" -ForegroundColor Red
        return $false
    }

    $imageFiles = Get-ChildItem $imagesDir -Filter "*.tar"
    if ($imageFiles.Count -eq 0) {
        Write-Host "No image files found in $imagesDir" -ForegroundColor Red
        return $false
    }

    $imported = 0
    foreach ($imageFile in $imageFiles) {
        Write-Host ("Importing: " + $imageFile.Name) -ForegroundColor Cyan
        try {
            & docker load -i $imageFile.FullName
            if ($LASTEXITCODE -eq 0) {
                $imported++
                Write-Host ("Imported: " + $imageFile.Name) -ForegroundColor Green
            } else {
                Write-Host ("✗ Failed to import: " + $imageFile.Name) -ForegroundColor Red
            }
        } catch {
            Write-Host ("✗ Error importing: " + $imageFile.Name + " - " + $_.Exception.Message) -ForegroundColor Red
        }
    }

    Write-Host ("Image import completed: $imported/$($imageFiles.Count) files imported") -ForegroundColor Green
    return $imported -gt 0
}

# Copy configuration and start services
function Start-OfflineServices {
    Write-Host "Starting services in offline mode..." -ForegroundColor Yellow

    # Copy docker-compose.yml from config directory
    $composeFile = Join-Path $ConfigDir "docker-compose.yml"
    if (-not (Test-Path $composeFile)) {
        Write-Host "docker-compose.yml not found in $ConfigDir" -ForegroundColor Red
        return $false
    }

    Copy-Item $composeFile "docker-compose.yml" -Force
    Write-Host "Configuration file copied" -ForegroundColor Green

    # Start services without building
    Write-Host "Starting Docker services..." -ForegroundColor Cyan
    & docker-compose up -d --no-build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services started successfully" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ Failed to start services" -ForegroundColor Red
        return $false
    }
}

# Wait for services to be ready
function Wait-ServicesReady {
    Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow

    $maxWait = 60
    $waited = 0

    while ($waited -lt $maxWait) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "Backend service is ready" -ForegroundColor Green
                break
            }
        } catch {
            # Continue waiting
        }

        Start-Sleep -Seconds 2
        $waited += 2
        Write-Host ("Waiting... (" + $waited + "/" + $maxWait + "s)") -ForegroundColor Cyan
    }

    if ($waited -ge $maxWait) {
        Write-Host "! Service startup timeout, but may still be initializing" -ForegroundColor Yellow
    }
}

# Show access information
function Show-AccessInfo {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Offline deployment completed!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access URLs:" -ForegroundColor Cyan
    Write-Host "- Frontend: http://localhost" -ForegroundColor White
    Write-Host "- Backend API: http://localhost:3000" -ForegroundColor White
    Write-Host "- MongoDB: localhost:27018" -ForegroundColor White
    Write-Host ""
    Write-Host "Management commands:" -ForegroundColor Cyan
    Write-Host "- Check status: docker-compose ps" -ForegroundColor White
    Write-Host "- View logs: docker-compose logs -f" -ForegroundColor White
    Write-Host "- Stop services: docker-compose down" -ForegroundColor White
    Write-Host "- Restart services: docker-compose restart" -ForegroundColor White
    Write-Host ""
    Write-Host "MongoDB connection: .\connect-mongodb.ps1" -ForegroundColor Yellow
    Write-Host ""
}

# Main execution
Write-Host ""

# 1. Check Docker environment
if (-not (Test-DockerEnvironment)) {
    Write-Host ""
    Write-Host "Please install and start Docker environment first" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 2. Check ports
if (-not (Test-PortAvailability)) {
    Write-Host ""
    Write-Host "Please free up occupied ports and try again" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 3. Import Docker images
if (-not (Import-DockerImages)) {
    Write-Host ""
    Write-Host "Failed to import Docker images" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 4. Start services (no build)
if (-not (Start-OfflineServices)) {
    Write-Host ""
    Write-Host "Failed to start services" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 5. Wait for services to be ready
Wait-ServicesReady

# 6. Show access information
Show-AccessInfo

Write-Host ""
Read-Host "Press any key to exit"
